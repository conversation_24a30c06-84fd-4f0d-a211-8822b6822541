@use '../utils/' as *;

/*============= COMMON CSS AREA ===============*/
.heading1 {
    h5 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-1);
        border-radius: 4px;
        padding: 8px 12px;
        text-transform: capitalize;
        display: inline-block;
        position: relative;
        z-index: 1;
        margin-bottom: 20px;
        &::after {
            position: absolute;
            content: "";
            height: 100%;
            width: 100%;
            left: 0;
            top: 0;
            border-radius: 4px;
            background: var(--ztc-text-text-1);
            opacity: 10%;
            z-index: -1;
        }
    }

    h1 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s64);
        line-height: var(--ztc-font-size-font-s70);
        font-weight: var(--ztc-weight-bold);
        color: var(--ztc-text-text-1);
        text-transform: capitalize;
        margin-bottom: 16px;
        @media #{$xs} {
            font-size: var(--ztc-font-size-font-s32);
            line-height: var(--ztc-font-size-font-s42);
        }
    }
    p {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        line-height: var(--ztc-font-size-font-s26);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-1);
        opacity: 0.9 !important;
        margin-bottom: 10px;
    }
}

.heading2 {
    h5 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-2);
        border-radius: 4px;
        padding: 8px 12px;
        text-transform: capitalize;
        display: inline-block;
        position: relative;
        z-index: 1;
        margin-bottom: 20px;
        &::after {
            position: absolute;
            content: "";
            height: 100%;
            width: 100%;
            left: 0;
            top: 0;
            border-radius: 4px;
            background: var(--ztc-bg-bg-2);
            z-index: -1;
        }
    }

    h2 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s44);
        line-height: var(--ztc-font-size-font-s54);
        font-weight: var(--ztc-weight-bold);
        color: var(--ztc-text-text-3);
        margin-bottom: 16px;
        @media #{$xs} {
            font-size: var(--ztc-font-size-font-s32);
            line-height: var(--ztc-font-size-font-s42);
        }
    }
    p {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        line-height: var(--ztc-font-size-font-s26);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-4);
        margin-bottom: 10px;
        opacity: 80% !important;
    }
}

.heading3 {
    h5 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        line-height: var(--ztc-font-size-font-s16);
        color: var(--ztc-text-text-1);
        font-weight: var(--ztc-weight-medium);
        text-transform: capitalize;
        border-radius: 4px;
        display: inline-block;
        background: var(--ztc-bg-bg-4);
        padding: 8px 12px;
        margin-bottom: 24px;
    }
    h1 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s64);
        line-height: var(--ztc-font-size-font-s70);
        font-weight: var(--ztc-weight-bold);
        color: var(--ztc-text-text-1);
        margin-bottom: 20px;
        @media #{$xs} {
            font-size: var(--ztc-font-size-font-s32);
            line-height: var(--ztc-font-size-font-s42);
        }
    }
     p {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-1);
        opacity: 80% !important;
        line-height: var(--ztc-font-size-font-s26);
     }
}

.heading4 {
    h5 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        line-height: var(--ztc-font-size-font-s16);
        color: var(--ztc-text-text-5);
        font-weight: var(--ztc-weight-medium);
        text-transform: capitalize;
        border-radius: 4px;
        display: inline-block;
        background: var(--ztc-bg-bg-5);
        padding: 8px 12px;
        margin-bottom: 16px;
    }
    h2 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s44);
        line-height: var(--ztc-font-size-font-s54);
        font-weight: var(--ztc-weight-bold);
        color: var(--ztc-text-text-5);
        margin-bottom: 16px;
        @media #{$xs} {
            font-size: var(--ztc-font-size-font-s32);
            line-height: var(--ztc-font-size-font-s42);
        }
    }
    p {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-6);
        line-height: var(--ztc-font-size-font-s26);
     }
}

.heading5 {
    h5 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        line-height: var(--ztc-font-size-font-s16);
        color: var(--ztc-bg-bg-7);
        font-weight: var(--ztc-weight-medium);
        text-transform: capitalize;
        border-radius: 4px;
        display: inline-block;
        background: var(--ztc-bg-bg-8);
        padding: 8px 12px;
        margin-bottom: 24px;
    }
    h1 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s64);
        line-height: var(--ztc-font-size-font-s70);
        font-weight: var(--ztc-weight-bold);
        color: var(--ztc-text-text-7);
        margin-bottom: 20px;
        @media #{$xs} {
            font-size: var(--ztc-font-size-font-s32);
            line-height: var(--ztc-font-size-font-s42);
        }
    }
     p {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-8);
        opacity: 80% !important;
        line-height: var(--ztc-font-size-font-s26);
     }
}

.heading6 {
    h5 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        line-height: var(--ztc-font-size-font-s16);
        color: var(--ztc-bg-bg-7);
        font-weight: var(--ztc-weight-medium);
        text-transform: capitalize;
        border-radius: 4px;
        display: inline-block;
        background: var(--ztc-bg-bg-10);
        padding: 8px 12px;
        margin-bottom: 16px;
    }
    h2 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s44);
        line-height: var(--ztc-font-size-font-s54);
        font-weight: var(--ztc-weight-bold);
        color: var(--ztc-text-text-7);
        margin-bottom: 16px;
        @media #{$xs} {
            font-size: var(--ztc-font-size-font-s32);
            line-height: var(--ztc-font-size-font-s42);
        }
    }
    p {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-8);
        opacity: 80% !important;
        line-height: var(--ztc-font-size-font-s26);
     }
}
.heading7 {
    h5 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        line-height: var(--ztc-font-size-font-s16);
        color: var(--ztc-text-text-1);
        font-weight: var(--ztc-weight-medium);
        text-transform: capitalize;
        border-radius: 4px;
        display: inline-block;
        padding: 8px 12px;
        margin-bottom: 16px;
        position: relative;
        &::after {
            position: absolute;
            content: "";
            height: 100%;
            width: 100%;
            left: 0;
            top: 0;
            border-radius: 4px;
            background: var(--ztc-text-text-1);
            opacity: 10%;
            z-index: -1;
        }
    }
    h2 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s44);
        line-height: var(--ztc-font-size-font-s54);
        font-weight: var(--ztc-weight-bold);
        color: var(--ztc-text-text-1);
        margin-bottom: 16px;
        @media #{$xs} {
            font-size: var(--ztc-font-size-font-s32);
            line-height: var(--ztc-font-size-font-s42);
        }
    }
    p {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-1);
        opacity: 80% !important;
        line-height: var(--ztc-font-size-font-s26);
     }
}

.heading8 {
    h5 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        line-height: var(--ztc-font-size-font-s16);
        color: var(--ztc-bg-bg-11);
        font-weight: var(--ztc-weight-medium);
        text-transform: capitalize;
        border-radius: 4px;
        display: inline-block;
        background: var(--ztc-bg-bg-12);
        padding: 8px 12px;
        margin-bottom: 16px;
    }
    h1 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s64);
        line-height: var(--ztc-font-size-font-s70);
        font-weight: var(--ztc-weight-bold);
        color: var(--ztc-text-text-7);
        margin-bottom: 20px;
        @media #{$xs} {
            font-size: var(--ztc-font-size-font-s32);
            line-height: var(--ztc-font-size-font-s42);
        }
    }
     p {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-8);
        opacity: 80% !important;
        line-height: var(--ztc-font-size-font-s26);
     }
}

.heading9 {
    h5 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        line-height: var(--ztc-font-size-font-s16);
        color: var(--ztc-bg-bg-11);
        font-weight: var(--ztc-weight-medium);
        text-transform: capitalize;
        border-radius: 4px;
        display: inline-block;
        background: var(--ztc-bg-bg-12);
        padding: 8px 12px;
        margin-bottom: 16px;
    }
    h2 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s44);
        line-height: var(--ztc-font-size-font-s54);
        font-weight: var(--ztc-weight-bold);
        color: var(--ztc-text-text-10);
        margin-bottom: 16px;
        @media #{$xs} {
            font-size: var(--ztc-font-size-font-s32);
            line-height: var(--ztc-font-size-font-s42);
        }
    }
    p {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-11);
        line-height: var(--ztc-font-size-font-s26);
     }
}

.heading10 {
    h5 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        line-height: var(--ztc-font-size-font-s16);
        color: var(--ztc-bg-bg-11);
        font-weight: var(--ztc-weight-medium);
        text-transform: capitalize;
        border-radius: 4px;
        display: inline-block;
        background: var(--ztc-bg-bg-12);
        padding: 8px 12px;
        margin-bottom: 16px;
    }
    h3 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s32);
        line-height: var(--ztc-font-size-font-s40);
        font-weight: var(--ztc-weight-bold);
        color: var(--ztc-text-text-10);
        margin-bottom: 16px;
        @media #{$xs} {
            font-size: var(--ztc-font-size-font-s32);
            line-height: var(--ztc-font-size-font-s42);
        }
    }
    p {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-11);
        line-height: var(--ztc-font-size-font-s26);
     }
}

.heading11 {
    h5 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s16);
        line-height: var(--ztc-font-size-font-s16);
        color: var(--ztc-text-text-1);
        font-weight: var(--ztc-weight-medium);
        text-transform: capitalize;
        border-radius: 4px;
        display: inline-block;
        background: #1A1F26;
        padding: 8px 12px;
        margin-bottom: 16px;
    }
    h2 {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s44);
        line-height: var(--ztc-font-size-font-s54);
        font-weight: var(--ztc-weight-bold);
        color: var(--ztc-text-text-1);
        margin-bottom: 16px;
        @media #{$xs} {
            font-size: var(--ztc-font-size-font-s32);
            line-height: var(--ztc-font-size-font-s42);
        }
    }
    p {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s18);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-1);
        line-height: var(--ztc-font-size-font-s26);
        opacity: 80% !important;
     }
}
// btn-area //
.header-btn1 {
    font-family: var(--ztc-family-font1);
    font-size: var(--ztc-font-size-font-s16);
    font-weight: var(--ztc-weight-bold);
    line-height: var(--ztc-font-size-font-s16);
    color: var(--ztc-text-text-1);
    background: var(--ztc-text-text-2);
    padding: 16px 20px;
    border-radius: 4px;
    display: inline-block;
    transition: all .4s;
    position: relative;
    z-index: 1;
    &:hover {
        color: var(--ztc-text-text-1);
        transition: all .4s;
        transform: translateY(-5px);
        &::after {
            width: 100%;
            transition: all .4s;
            right: auto;
            left: 0;
        }
    }
    &::after {
        position: absolute;
        content: "";
        height: 100%;
        width: 0;
        right: 0;
        top: 0;
        transition: all .4s;
        background: var(--ztc-text-text-1);
        opacity: 0.2;
        z-index: -1;
        border-radius: 4px;
    }
    i {
        margin-left: 4px;
        transform: rotate(-45deg);
    }
}
.header-btn2 {
    font-family: var(--ztc-family-font1);
    font-size: var(--ztc-font-size-font-s16);
    font-weight: var(--ztc-weight-bold);
    line-height: var(--ztc-font-size-font-s16);
    color: var(--ztc-text-text-1);
    padding: 14px 20px;
    border-radius: 4px;
    display: inline-block;
    transition: all .4s;
    position: relative;
    z-index: 1;
    border: 1px solid var(--ztc-text-text-1);
    &:hover {
        color: var(--ztc-text-text-1);
        transition: all .4s;
        transform: translateY(-5px);
        border: 1px solid var(--ztc-text-text-2);
        &::after {
            width: 100%;
            transition: all .4s;
            right: auto;
            left: 0;
        }
    }
    &::after {
        position: absolute;
        content: "";
        height: 100%;
        width: 0;
        right: 0;
        top: 0;
        transition: all .4s;
        background: var(--ztc-text-text-2);
        z-index: -1;
        border-radius: 4px;
    }
    i {
        margin-left: 4px;
        transform: rotate(-45deg);
    }
}

.header-btn3 {
    font-family: var(--ztc-family-font1);
    font-size: var(--ztc-font-size-font-s16);
    font-weight: var(--ztc-weight-bold);
    line-height: var(--ztc-font-size-font-s16);
    color: var(--ztc-text-text-5);
    background: var(--ztc-bg-bg-3);
    padding: 16px 20px;
    border-radius: 4px;
    display: inline-block;
    transition: all .4s;
    position: relative;
    z-index: 1;
    &:hover {
        color: var(--ztc-text-text-1);
        transition: all .4s;
        &::after {
            left: 0;
            top: 0;
            visibility: visible;
            opacity: 1;
            transition: all .4s;
            border-radius: 4px;
            width: 100%;
            height: 100%;
        }
    }
    &::after {
        position: absolute;
        content: '';
        height: 20px;
        width: 20px;
        border-radius: 50%;
        left: 45%;
        top: 27%;
        transition: all .4s;
        background: var(--ztc-text-text-5);
        z-index: -1;
        visibility: hidden;
        opacity: 0;
    }
    i {
        margin-left: 4px;
        transform: rotate(-45deg);
    }
}

.header-btn4 {
    font-family: var(--ztc-family-font1);
    font-size: var(--ztc-font-size-font-s16);
    font-weight: var(--ztc-weight-bold);
    line-height: var(--ztc-font-size-font-s16);
    color: var(--ztc-text-text-1);
    padding: 14px 20px;
    border-radius: 4px;
    display: inline-block;
    transition: all .4s;
    position: relative;
    z-index: 1;
    border: 1px solid var(--ztc-text-text-1);
    &:hover {
        color: var(--ztc-text-text-5);
        transition: all .4s;
        &::after {
            left: 0;
            top: 0;
            visibility: visible;
            opacity: 1;
            transition: all .4s;
            border-radius: 4px;
            width: 100%;
            height: 100%;
        }
    }
    &::after {
        position: absolute;
        content: '';
        height: 20px;
        width: 20px;
        border-radius: 50%;
        left: 45%;
        top: 27%;
        transition: all .4s;
        background: var(--ztc-text-text-1);
        z-index: -1;
        visibility: hidden;
        opacity: 0;
    }
    i {
        margin-left: 4px;
        transform: rotate(-45deg);
    }
}

.header-btn5 {
    font-family: var(--ztc-family-font1);
    font-size: var(--ztc-font-size-font-s16);
    font-weight: var(--ztc-weight-bold);
    line-height: var(--ztc-font-size-font-s16);
    color: var(--ztc-text-text-1);
    background: var(--ztc-bg-bg-7);
    padding: 16px 20px;
    border-radius: 4px;
    display: inline-block;
    transition: all .4s;
    position: relative;
    z-index: 1;
    &:hover {
        color: var(--ztc-text-text-1);
        transition: all .4s;
        &::after {
            left: 0;
            top: 0;
            visibility: visible;
            opacity: 1;
            transition: all .4s;
            border-radius: 4px;
            width: 100%;
            height: 100%;
        }
    }
    &::after {
        position: absolute;
        content: '';
        height: 100%;
        width: 1px;
        border-radius: 4px;
        left: 45%;
        top: 0;
        transition: all .4s;
        background: #ff6a00;
        z-index: -1;
        visibility: hidden;
        opacity: 0;
    }
    i {
        margin-left: 4px;
        transform: rotate(-45deg);
    }
}

.header-btn6 {
    font-family: var(--ztc-family-font1);
    font-size: var(--ztc-font-size-font-s16);
    font-weight: var(--ztc-weight-bold);
    line-height: var(--ztc-font-size-font-s16);
    color: var(--ztc-text-text-7);
    padding: 14px 20px;
    border-radius: 4px;
    display: inline-block;
    transition: all .4s;
    position: relative;
    z-index: 1;
    border: 1px solid var(--ztc-text-text-7);
    &:hover {
        color: var(--ztc-text-text-1);
        transition: all .4s;
        border: 1px solid #ff6a00;
        &::after {
            left: 0;
            top: 0;
            visibility: visible;
            opacity: 1;
            transition: all .4s;
            border-radius: 4px;
            width: 100%;
            height: 100%;
        }
    }
    &::after {
        position: absolute;
        content: '';
        height: 100%;
        width: 1px;
        border-radius: 4px;
        left: 45%;
        top: 0;
        transition: all .4s;
        background: #ff6a00;
        z-index: -1;
        visibility: hidden;
        opacity: 0;
    }
    i {
        margin-left: 4px;
        transform: rotate(-45deg);
    }
}

.header-btn7 {
    font-family: var(--ztc-family-font1);
    font-size: var(--ztc-font-size-font-s16);
    font-weight: var(--ztc-weight-bold);
    line-height: var(--ztc-font-size-font-s16);
    color: var(--ztc-text-text-1);
    background: var(--ztc-bg-bg-11);
    padding: 16px 20px;
    border-radius: 4px;
    display: inline-block;
    transition: all .4s;
    position: relative;
    z-index: 1;
    &:hover {
        color: var(--ztc-text-text-1);
        transition: all .4s;
        transform: translateY(-5px);
        &::after {
            left: 0;
            top: 0;
            visibility: visible;
            opacity: 1;
            transition: all .4s;
            border-radius: 4px;
            width: 100%;
            height: 100%;
        }
    }
    &::after {
        position: absolute;
        content: '';
        height: 100%;
        width: 1px;
        border-radius: 4px;
        left: 45%;
        top: 0;
        transition: all .4s;
        background: var(--ztc-text-text-7);
        z-index: -1;
        visibility: hidden;
        opacity: 0;
    }
    i {
        margin-left: 4px;
        transform: rotate(-45deg);
    }
}

.header-btn8 {
    font-family: var(--ztc-family-font1);
    font-size: var(--ztc-font-size-font-s16);
    font-weight: var(--ztc-weight-bold);
    line-height: var(--ztc-font-size-font-s16);
    color: var(--ztc-bg-bg-11);
    padding: 16px 20px;
    border-radius: 4px;
    display: inline-block;
    transition: all .4s;
    position: relative;
    z-index: 1;
    background: var(--ztc-text-text-1);
    &:hover {
        color: var(--ztc-text-text-1);
        transition: all .4s;
        transform: translateY(-5px);
        &::after {
            left: 0;
            top: 0;
            visibility: visible;
            opacity: 1;
            transition: all .4s;
            border-radius: 4px;
            width: 100%;
            height: 100%;
        }
    }
    &::after {
        position: absolute;
        content: '';
        height: 100%;
        width: 1px;
        border-radius: 4px;
        left: 45%;
        top: 0;
        transition: all .4s;
        background: var(--ztc-text-text-7);
        z-index: -1;
        visibility: hidden;
        opacity: 0;
    }
    i {
        margin-left: 4px;
        transform: rotate(-45deg);
    }
}


/*============= COMMON CSS AREA ENDS===============*/