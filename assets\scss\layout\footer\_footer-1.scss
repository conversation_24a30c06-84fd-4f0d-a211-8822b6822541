@use '../../utils/' as * ;

/*============= FOOTER CSS AREA ===============*/
.footer1-section-area {
    position: relative;
    z-index: 1;
    background: var(--ztc-text-text-3);
    .footer-logo-content {
        border-bottom: 1px solid #1A1F26;
       .logo-content {
        p {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            line-height: var(--ztc-font-size-font-s24);
            font-weight: var(--ztc-weight-medium);
            color: var(--ztc-text-text-1);
            opacity: 80%;
            margin-top: 24px;
        }
        ul {
            margin-top: 24px;
            li {
                display: inline-block;
                a {
                    height: 36px;
                    width: 36px;
                    text-align: center;
                    line-height: 36px;
                    display: inline-block;
                    transition: all .4s;
                    background: #1A1E26;
                    border-radius: 50%;
                    color: var(--ztc-text-text-1);
                    margin: 0 6px 0 0;
                    &:hover {
                        background: var(--ztc-text-text-2);
                        transition: all .4s;
                        color: var(--ztc-text-text-1);
                        transform: translateY(-3px);
                    }
                }
            }
        }
       }
       .service-heading {
        h2 {
            font-family: var(--ztc-font-size-font-s20);
            font-weight: var(--ztc-weight-bold);
            color: var(--ztc-text-text-1);
            font-size: var(--ztc-font-size-font-s20);
            text-transform: capitalize;
            margin-bottom: 4px;
        }
        ul {
            li {
                a {
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s18);
                    font-weight: var(--ztc-weight-medium);
                    color: var(--ztc-text-text-1);
                    opacity: 80%;
                    transition: all .4s;
                    display: inline-block;
                    padding-top: 20px;
                    &:hover {
                        color: var(--ztc-text-text-2);
                        transition: all .4s;
                        padding-left: 5px;
                    }
                }
            }
        }
       }
       .service-heading.contact {
        ul {
            li {
                a {
                    display: flex;

                    &:hover {
                        .icons {
                            color: var(--ztc-text-text-2);
                            transition: all .4s;
                        }
                    }
                    span.icons {
                        height: 30px;
                        width: 30px;
                        text-align: center;
                        line-height: 30px;
                        border-radius: 50%;
                        background: #01060E;     
                        transition: all .4s;       
                        display: inline-block;  
                        margin: 0 8px 0 0;          
                    }
                }

            }
        }
       }
    }
    .copyright {
        p {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            font-weight: var(--ztc-weight-medium);
            color: var(--ztc-text-text-1);
            opacity: 80%;
            line-height: var(--ztc-font-size-font-s24);
            text-align: center;
            padding: 24px 0 30px 0;
        }
    }
}

// homepage2 //
.footer2-section-area {
    position: relative;
    z-index: 1;
    background: var(--ztc-text-text-5);
    .cta-header {
        padding-top: 100px;
        @media #{$xs} {
            padding-top: 50px;
        }
        @media #{$md} {
            padding-top: 50px;
        }
        h2 {
            color: var(--ztc-text-text-1);
        }
        p {
            color: var(--ztc-text-text-1);
            opacity: 0.8 !important;
            font-size: var(--ztc-font-size-font-s18);
            line-height: var(--ztc-font-size-font-s26);
        }
        form {
            background: var(--ztc-text-text-1);
            border-radius: 4px;
            padding: 20px;
            position: relative;
            height: 56px;
            margin-top: 32px;
            display: inline-block;
            width: 385px;
            @media #{$xs} {
                width: 100%;
            }
            @media #{$md} {
                width: 100%;
            }
            input {
                font-family: var(--ztc-family-font1);
                font-size: var( --ztc-font-size-font-s16);
                line-height: var(--ztc-font-size-font-s16);
                color: var(--ztc-text-text-5);
                font-weight: var(--ztc-weight-medium);
                width: 100%;
                outline: none;
                &::placeholder {
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-weight: var(--ztc-weight-medium);
                    color: var(--ztc-text-text-5);
                }
            }
            button {
                border: none;
                transition: all .4s;
                position: absolute;
                right: 4px;
                top: 4px;
            }
        }
    }
    .footer-logo-content {
        border-bottom: 1px solid #2C3C4B;
       .logo-content {
        p {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            line-height: var(--ztc-font-size-font-s24);
            font-weight: var(--ztc-weight-medium);
            color: var(--ztc-text-text-1);
            opacity: 80%;
            margin-top: 24px;
        }
        ul {
            margin-top: 24px;
            li {
                display: inline-block;
                a {
                    height: 36px;
                    width: 36px;
                    text-align: center;
                    line-height: 36px;
                    display: inline-block;
                    transition: all .4s;
                    background: #2C3B4A;
                    border-radius: 50%;
                    color: var(--ztc-text-text-1);
                    margin: 0 6px 0 0;
                    &:hover {
                        background: var(--ztc-bg-bg-3);
                        transition: all .4s;
                        color: var(--ztc-text-text-5);
                        transform: translateY(-3px);
                    }
                }
            }
        }
       }
       .service-heading {
        h2 {
            font-family: var(--ztc-font-size-font-s20);
            font-weight: var(--ztc-weight-bold);
            color: var(--ztc-text-text-1);
            font-size: var(--ztc-font-size-font-s20);
            text-transform: capitalize;
            margin-bottom: 4px;
        }
        ul {
            li {
                a {
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s18);
                    font-weight: var(--ztc-weight-medium);
                    color: var(--ztc-text-text-1);
                    opacity: 0.8 !important;
                    transition: all .4s;
                    display: inline-block;
                    padding-top: 20px;
                    &:hover {
                        color: var(--ztc-bg-bg-3);
                        transition: all .4s;
                        padding-left: 5px;
                    }
                }
            }
        }
       }
       .service-heading.contact {
        ul {
            li {
                a {
                    display: flex;
                    &:hover {
                        .icons {
                            color: var(--ztc-bg-bg-3);
                            transition: all .4s;
                        }
                    }
                    .icons {
                        margin: 0 8px 0 0;
                        transition: all .4s;
                    }
                }

            }
        }
       }
    }
    .copyright {
        p {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            font-weight: var(--ztc-weight-medium);
            color: var(--ztc-text-text-1);
            opacity: 80%;
            line-height: var(--ztc-font-size-font-s24);
            text-align: center;
            padding: 24px 0 30px 0;
        }
    }
}

// homepage3 //
.footer3-section-area {
    position: relative;
    z-index: 1;
    background: var(--ztc-text-text-7);
    .cta-header {
        padding-top: 100px;
        @media #{$xs} {
            padding-top: 50px;
        }
        @media #{$md} {
            padding-top: 50px;
        }
        h2 {
            color: var(--ztc-text-text-1);
        }
        p {
            color: var(--ztc-text-text-1);
            opacity: 0.8 !important;
            font-size: var(--ztc-font-size-font-s18);
            line-height: var(--ztc-font-size-font-s26);
        }
        form {
            background: var(--ztc-text-text-1);
            border-radius: 4px;
            padding: 20px;
            position: relative;
            height: 56px;
            margin-top: 32px;
            display: inline-block;
            width: 385px;
            @media #{$xs} {
                width: 100%;
            }
            @media #{$md} {
                width: 100%;
            }
            input {
                font-family: var(--ztc-family-font1);
                font-size: var( --ztc-font-size-font-s16);
                line-height: var(--ztc-font-size-font-s16);
                color: var(--ztc-text-text-5);
                font-weight: var(--ztc-weight-medium);
                width: 100%;
                outline: none;
                &::placeholder {
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-weight: var(--ztc-weight-medium);
                    color: var(--ztc-text-text-5);
                }
            }
            button {
                border: none;
                transition: all .4s;
                position: absolute;
                right: 4px;
                top: 4px;
            }
        }
    }
    .footer-logo-content {
        border-bottom: 1px solid #221D19;
       .logo-content {
        p {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            line-height: var(--ztc-font-size-font-s24);
            font-weight: var(--ztc-weight-medium);
            color: var(--ztc-text-text-1);
            opacity: 80%;
            margin-top: 24px;
        }
        ul {
            margin-top: 24px;
            li {
                display: inline-block;
                a {
                    height: 36px;
                    width: 36px;
                    text-align: center;
                    line-height: 36px;
                    display: inline-block;
                    transition: all .4s;
                    background: #221D19;
                    border-radius: 50%;
                    color: var(--ztc-text-text-1);
                    margin: 0 6px 0 0;
                    &:hover {
                        background: var(--ztc-bg-bg-7);
                        transition: all .4s;
                        color: var(--ztc-text-text-1);
                        transform: translateY(-3px);
                    }
                }
            }
        }
       }
       .service-heading {
        h2 {
            font-family: var(--ztc-font-size-font-s20);
            font-weight: var(--ztc-weight-bold);
            color: var(--ztc-text-text-1);
            font-size: var(--ztc-font-size-font-s20);
            text-transform: capitalize;
            margin-bottom: 4px;
        }
        ul {
            li {
                a {
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s18);
                    font-weight: var(--ztc-weight-medium);
                    color: var(--ztc-text-text-1);
                    opacity: 0.8 !important;
                    transition: all .4s;
                    display: inline-block;
                    padding-top: 20px;
                    &:hover {
                        color: var(--ztc-bg-bg-7);
                        transition: all .4s;
                        padding-left: 5px;
                    }
                }
            }
        }
       }
       .service-heading.contact {
        ul {
            li {
                a {
                    display: flex;
                    &:hover {
                        .icons {
                            color: var(--ztc-bg-bg-7);
                            transition: all .4s;
                        }
                    }
                    .icons {
                        margin: 0 8px 0 0;
                        transition: all .4s;
                    }
                }

            }
        }
       }
    }
    .copyright {
        p {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            font-weight: var(--ztc-weight-medium);
            color: var(--ztc-text-text-1);
            opacity: 80%;
            line-height: var(--ztc-font-size-font-s24);
            text-align: center;
            padding: 24px 0 30px 0;
        }
    }
}

// homepage4 //
.footer4-section-area {
    position: relative;
    z-index: 1;
    background: var(--ztc-text-text-10);
    .footer-logo-content {
        border-bottom: 1px solid #1A1F26;
       .logo-content {
        p {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            line-height: var(--ztc-font-size-font-s24);
            font-weight: var(--ztc-weight-medium);
            color: var(--ztc-text-text-1);
            opacity: 80%;
            margin-top: 24px;
        }
        ul {
            margin-top: 24px;
            li {
                display: inline-block;
                a {
                    height: 36px;
                    width: 36px;
                    text-align: center;
                    line-height: 36px;
                    display: inline-block;
                    transition: all .4s;
                    background: #1A1E26;
                    border-radius: 50%;
                    color: var(--ztc-text-text-1);
                    margin: 0 6px 0 0;
                    &:hover {
                        background: var(--ztc-bg-bg-11);
                        transition: all .4s;
                        color: var(--ztc-text-text-1);
                        transform: translateY(-3px);
                    }
                }
            }
        }
       }
       .service-heading {
        h2 {
            font-family: var(--ztc-font-size-font-s20);
            font-weight: var(--ztc-weight-bold);
            color: var(--ztc-text-text-1);
            font-size: var(--ztc-font-size-font-s20);
            text-transform: capitalize;
            margin-bottom: 4px;
        }
        ul {
            li {
                a {
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s18);
                    font-weight: var(--ztc-weight-medium);
                    color: var(--ztc-text-text-1);
                    opacity: 80%;
                    transition: all .4s;
                    display: inline-block;
                    padding-top: 20px;
                    &:hover {
                        color: var(--ztc-text-text-1);
                        transition: all .4s;
                        padding-left: 5px;
                    }
                }
            }
        }
       }
       .service-heading.contact {
        ul {
            li {
                a {
                    display: flex;

                    &:hover {
                        .icons {
                            background: none;
                            transition: all .4s;
                        }
                    }
                    span.icons {
                        height: 30px;
                        width: 30px;
                        text-align: center;
                        line-height: 30px;
                        border-radius: 50%;
                        background: #01060E;     
                        transition: all .4s;       
                        display: inline-block;  
                        margin: 0 8px 0 0;          
                    }
                }

            }
        }
       }
    }
    .copyright {
        p {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            font-weight: var(--ztc-weight-medium);
            color: var(--ztc-text-text-1);
            opacity: 80%;
            line-height: var(--ztc-font-size-font-s24);
            text-align: center;
            padding: 24px 0 30px 0;
        }
    }
}
/*============= FOOTER CSS AREA ===============*/
