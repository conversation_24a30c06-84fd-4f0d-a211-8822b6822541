@use '../../utils/' as * ;

/*============= OTHERS CSS AREA STARTS ===============*/
// progress //
.progress-wrap {
    position: fixed;
    right: 30px;
    bottom: 30px;
    height: 56px;
    width: 56px;
    cursor: pointer;
    display: block;
    border-radius: 50px;
    box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(15px);
    -webkit-transition: all 200ms linear;
    transition: all 200ms linear;
    &:hover{
        background: var(--ztc-text-text-2);
        transform: translateY(-5px);
        box-shadow: 0 0 15px 0 var(--ztc-text-text-2);
        transition: all .4s;
        color: var(--ztc-text-text-1);
    }
   }
   .progress-wrap.active-progress {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
   }
   .progress-wrap::after {
    position: absolute;
    font-family: "FontAwesome";
    content: "\f062";
    text-align: center;
    line-height: 56px;
    font-size: 18px;
    color: var(--ztc-text-text-2);
    left: 0;
    top: 0;
    height: 56px;
    width: 56px;
    cursor: pointer;
    display: block;
    z-index: 1;
    -webkit-transition: all 200ms linear;
    transition: all 200ms linear;
   }
   .progress-wrap:hover::after {
    opacity: 0;
   }
   .progress-wrap::before {
    position: absolute;
    font-family: "FontAwesome";
    content: "\f062";
    text-align: center;
    line-height: 56px;
    font-size: 18px;
    opacity: 0;
    left: 0;
    top: 0;
    height: 56px;
    width: 56px;
    cursor: pointer;
    display: block;
    z-index: 2;
    -webkit-transition: all 200ms linear;
    transition: all 200ms linear;
   }
   .progress-wrap:hover::before {
    opacity: 1;
   }
   .progress-wrap svg path {
    fill: none;
   }
   .progress-wrap svg.progress-circle path {
    stroke: var(--ztc-text-text-2);
    stroke-width: 4;
    box-sizing: border-box;
    -webkit-transition: all 200ms linear;
    transition: all 200ms linear;
   }
   .progress-wrap.active-progress {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
   }

.pagination2 {
    .progress-wrap {
        position: fixed;
        right: 30px;
        bottom: 30px;
        height: 56px;
        width: 56px;
        cursor: pointer;
        display: block;
        border-radius: 50px;
        box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        transform: translateY(15px);
        -webkit-transition: all 200ms linear;
        transition: all 200ms linear;
        &:hover{
            background: var(--ztc-bg-bg-3);
            transform: translateY(-5px);
            box-shadow: 0 0 15px 0 var(--ztc-bg-bg-3);
            transition: all .4s;
        }
       }
       .progress-wrap.active-progress {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
       }
       .progress-wrap::after {
        position: absolute;
        font-family: "FontAwesome";
        content: "\f062";
        text-align: center;
        line-height: 56px;
        font-size: 18px;
        color: var(--ztc-bg-bg-3);
        left: 0;
        top: 0;
        height: 56px;
        width: 56px;
        cursor: pointer;
        display: block;
        z-index: 1;
        -webkit-transition: all 200ms linear;
        transition: all 200ms linear;
       }
       .progress-wrap:hover::after {
        opacity: 0;
       }
       .progress-wrap::before {
        position: absolute;
        font-family: "FontAwesome";
        content: "\f062";
        text-align: center;
        line-height: 56px;
        font-size: 18px;
        opacity: 0;
        left: 0;
        top: 0;
        height: 56px;
        width: 56px;
        cursor: pointer;
        display: block;
        z-index: 2;
        -webkit-transition: all 200ms linear;
        transition: all 200ms linear;
       }
       .progress-wrap:hover::before {
        opacity: 1;
       }
       .progress-wrap svg path {
        fill: none;
       }
       .progress-wrap svg.progress-circle path {
        stroke: var(--ztc-bg-bg-3);
        stroke-width: 4;
        box-sizing: border-box;
        -webkit-transition: all 200ms linear;
        transition: all 200ms linear;
       }
       .progress-wrap.active-progress {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
       }
}
.pagination3 {
    .progress-wrap {
        position: fixed;
        right: 30px;
        bottom: 30px;
        height: 56px;
        width: 56px;
        cursor: pointer;
        display: block;
        border-radius: 50px;
        box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        transform: translateY(15px);
        -webkit-transition: all 200ms linear;
        transition: all 200ms linear;
        &:hover{
            background: var(--ztc-bg-bg-7);
            transform: translateY(-5px);
            box-shadow: 0 0 15px 0 var(--ztc-bg-bg-7);
            transition: all .4s;
        }
       }
       .progress-wrap.active-progress {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
       }
       .progress-wrap::after {
        position: absolute;
        font-family: "FontAwesome";
        content: "\f062";
        text-align: center;
        line-height: 56px;
        font-size: 18px;
        color: var(--ztc-bg-bg-7);
        left: 0;
        top: 0;
        height: 56px;
        width: 56px;
        cursor: pointer;
        display: block;
        z-index: 1;
        -webkit-transition: all 200ms linear;
        transition: all 200ms linear;
       }
       .progress-wrap:hover::after {
        opacity: 0;
       }
       .progress-wrap::before {
        position: absolute;
        font-family: "FontAwesome";
        content: "\f062";
        text-align: center;
        line-height: 56px;
        font-size: 18px;
        opacity: 0;
        left: 0;
        top: 0;
        height: 56px;
        width: 56px;
        cursor: pointer;
        display: block;
        z-index: 2;
        -webkit-transition: all 200ms linear;
        transition: all 200ms linear;
       }
       .progress-wrap:hover::before {
        opacity: 1;
       }
       .progress-wrap svg path {
        fill: none;
       }
       .progress-wrap svg.progress-circle path {
        stroke: var(--ztc-bg-bg-7);
        stroke-width: 4;
        box-sizing: border-box;
        -webkit-transition: all 200ms linear;
        transition: all 200ms linear;
       }
       .progress-wrap.active-progress {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
       }
}

.pagination4 {
    .progress-wrap {
        position: fixed;
        right: 30px;
        bottom: 30px;
        height: 56px;
        width: 56px;
        cursor: pointer;
        display: block;
        border-radius: 50px;
        box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        transform: translateY(15px);
        -webkit-transition: all 200ms linear;
        transition: all 200ms linear;
        &:hover{
            background: var(--ztc-bg-bg-11);
            transform: translateY(-5px);
            box-shadow: 0 0 15px 0 var(--ztc-bg-bg-11);
            transition: all .4s;
        }
       }
       .progress-wrap.active-progress {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
       }
       .progress-wrap::after {
        position: absolute;
        font-family: "FontAwesome";
        content: "\f062";
        text-align: center;
        line-height: 56px;
        font-size: 18px;
        color: var(--ztc-bg-bg-11);
        left: 0;
        top: 0;
        height: 56px;
        width: 56px;
        cursor: pointer;
        display: block;
        z-index: 1;
        -webkit-transition: all 200ms linear;
        transition: all 200ms linear;
       }
       .progress-wrap:hover::after {
        opacity: 0;
       }
       .progress-wrap::before {
        position: absolute;
        font-family: "FontAwesome";
        content: "\f062";
        text-align: center;
        line-height: 56px;
        font-size: 18px;
        opacity: 0;
        left: 0;
        top: 0;
        height: 56px;
        width: 56px;
        cursor: pointer;
        display: block;
        z-index: 2;
        -webkit-transition: all 200ms linear;
        transition: all 200ms linear;
       }
       .progress-wrap:hover::before {
        opacity: 1;
       }
       .progress-wrap svg path {
        fill: none;
       }
       .progress-wrap svg.progress-circle path {
        stroke: var(--ztc-bg-bg-11);
        stroke-width: 4;
        box-sizing: border-box;
        -webkit-transition: all 200ms linear;
        transition: all 200ms linear;
       }
       .progress-wrap.active-progress {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
       }
}



   .cursor {
    width: 30px;
    height: 30px;
    border: 1px solid var(--ztc-text-text-2);
    border-radius: 50%;
    position: absolute;
    z-index: 9999;
    transition-duration: 200ms;
    transition-timing-function: ease-out;
    animation: cursorAnim .5s  alternate;
    pointer-events: none;
}

.cursor::after {
    content: "";
    width: 10px;
    height: 10px;
    position: absolute;
    border: 2px solid var(--ztc-text-text-2);
    border-radius: 50%;
    opacity: .5;
    top: 9PX;
    left: 9PX;
    animation: cursorAnim2 .5s  alternate;
    background: var(--ztc-text-text-2);
}

@keyframes cursorAnim {
    from {
        transform: scale(1);
    }
    to {
        transform: scale(.7);
    }
}

@keyframes cursorAnim2 {
    from {
        transform: scale(1);
    }
    to {
        transform: scale(.4);
    }
}

@keyframes cursorAnim3 {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(3);
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

.expand {
    animation: cursorAnim3 .5s forwards;
    border: 1px solid var(--ztc-text-text-2);
}

// preloader //
.preloader{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999999999;
	background-color: var(--ztc-text-text-1);
	display: flex;
	align-items: center;
	justify-content: center;
}

.preloader2{
    background: var(--ztc-bg-bg-11) !important;
}
.preloader3{
    background: var(--ztc-text-text-5) !important;
}
.preloader4{
    background: var(--ztc-bg-bg-7) !important;
}
.loading-container,
.loading{
	height: 120px;
	position: relative;
	width: 120px;
	border-radius: 100%;
}

.loading-container{
	margin: 40px auto
}

.loading{
	border: 1px solid transparent;
	border-color: transparent var(--ztc-text-text-2) transparent var(--ztc-text-text-2);
	animation: rotate-loading 1.5s linear 0s infinite normal;
	transform-origin: 50% 50%;
}
.preloader2 {
    .loading {
        border-color: transparent var(--ztc-text-text-1) transparent var(--ztc-text-text-1) !important;
        border: 1px solid transparent;
        animation: rotate-loading 1.5s linear 0s infinite normal;
        transform-origin: 50% 50%;
    }
}
.preloader3 {
    .loading {
        border-color: transparent var(--ztc-bg-bg-3) transparent var(--ztc-bg-bg-3) !important;
        border: 1px solid transparent;
        animation: rotate-loading 1.5s linear 0s infinite normal;
        transform-origin: 50% 50%;
    }
}
.preloader4 {
    .loading {
        border-color: transparent var(--ztc-text-text-1) transparent var(--ztc-text-text-1) !important;
        border: 1px solid transparent;
        animation: rotate-loading 1.5s linear 0s infinite normal;
        transform-origin: 50% 50%;
    }
}

.loading-container:hover .loading,
.loading-container .loading{
	transition: all 0.5s ease-in-out;
}

#loading-icon{
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
    height: 80px;
    width: 80px;
}

@keyframes rotate-loading{
	0%{
		transform: rotate(0deg);
	}

	100%{
		transform: rotate(360deg);
	}
}


/*============= OTHERS CSS AREA ENDS ===============*/

/*============= PRICING CSS AREA ===============*/

.pricing-area {
    position: relative;
    z-index: 2;
    overflow-x: hidden;
  }

  .toggle-inner input{
      position: absolute;
      left: 0;
      width: 100%;
      height: 100%;
      margin: 0;
      border-radius: 25px;
      right: 0;
      z-index: 1;
      opacity: 0;
      cursor: pointer;
    }


  .custom-toggle {
    position: absolute;
    height: 20px;
    width: 20px;
    background-color: var(--ztc-text-text-1);
    top: 5px;
    left: 35px;
    border-radius: 50%;
    transition: 300ms all;
  }

  .toggle-inner .t-month,
  .toggle-inner .t-year {
    position: absolute;
    left: -75px;
    top: 2px;
    transition: 300ms all;
  }

  .toggle-inner .t-year {
    left: unset;
    left: 73px;
    opacity: 0.5;
  }

  .active > .toggle-inner .t-month {
    opacity: 0.5;
  }

  .active > .toggle-inner .t-year {
    opacity: 1;
  }

  .toggle-inner input:checked + span {
    left: 5px;
  }

  .toggle-inner {
    width: 60px;
    margin: 0 auto;
    height: 30px;
    border-radius: 25px;
    position: relative;
    background: var(--ztc-text-text-3);
    left: -20px;
  }

  .t-year h4 {
    min-width: 200px;
  }
  .t-year {
    text-align: left;
  }
.plan-toggle-wrap {
    margin-top: 50px;
    margin-bottom: 32px;
}
  .plan-toggle-wrap h4 {
    font-size: var(--ztc-font-size-font-s16);
    font-weight: var(--ztc-weight-bold);
    color: var(--ztc-text-text-3);
    font-family: "Figtree", sans-serif;
    margin-bottom: 0;
}

.plan-toggle-wrap h4 span {
    color: var(--ztc-text-text-2);
    font-family: var(--ztc-family-font1);
}
.plan-toggle-wrap1 {
    position: absolute;
    z-index: 1;
    left: 25%;
    top: 30%;
    right: 71%;
    @media #{$xs} {
        position: relative;
        left: 0;
        top: 0;
        text-align: center;
        right: 0;
        margin-bottom: 20px;
    }
    @media #{$md} {
        left: 20%;
        top: 20%;
        right: 0;
    }
    @media #{$xxxxl} {
        right: 56% !important;
    }
}
  .plan-toggle-wrap1 h4 {
    font-size: var(--ztc-font-size-font-s16);
    font-weight: var(--ztc-weight-bold);
    color: var(--ztc-text-text-3);
    font-family: "Figtree", sans-serif;
    margin-bottom: 0;
}

.plan-toggle-wrap1 h4 span {
    color: var(--ztc-text-text-2);
    font-family: var(--ztc-family-font1);
}

.single-pricing-area {
    margin-bottom: 30px;
    background: var(--ztc-bg-bg-1);
    border-radius: 4px;
    padding: 30px;
    border: 1px solid var(--ztc-bg-bg-1);
    transition: all .4s;
    &:hover {
        border: 1px solid var(--ztc-text-text-2);
        transition: all .4s;
    }
    .pricing-box {
        h3 {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            font-weight: var(--ztc-weight-bold);
            color: var(--ztc-text-text-3);
            line-height: var(--ztc-font-size-font-s18);
            margin-bottom: 20px;
        }
        p {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-text-text-4);
            line-height: var(--ztc-font-size-font-s26);
            font: var(--ztc-font-size-font-s16);
            color: var(--ztc-text-text-4);
            font-weight: var(--ztc-weight-medium);
        }

        h2 {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s44);
            line-height: var(--ztc-font-size-font-s44);
            color: var(--ztc-text-text-3);
            font-weight: var(--ztc-weight-bold);
            margin-bottom: 4px;
            margin-top: 24px;
        }
        .header-btn1 {
            width: 100%;
            margin-top: 24px;
            text-align: center;
        }
        h4 {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s20);
            line-height: var(--ztc-font-size-font-s20);
            color: var(--ztc-text-text-3);
            margin-top: 24px;
            margin-bottom: 24px;
            font-weight: var(--ztc-weight-bold);
        }
        ul {
            li {
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s16);
                line-height: var(--ztc-font-size-font-s16);
                color: var(--ztc-text-text-4);
                font-weight: var(--ztc-weight-medium);
                margin-top: 16px;
                img {
                    margin: 0 8px 0 0;
                }
            }
        }
    }
}
.single-pricing-area.active {
    border: 1px solid var(--ztc-text-text-2);
    transition: all .4s;
}


// compare plan //

.compareplan-section-area {
    position: relative;
    z-index: 1;
    background: var(--ztc-bg-bg-1);
    .single-pricing-area {
        background: var(--ztc-text-text-1);
        padding: 32px 40px !important;
        position: relative;
        margin-bottom: 0;
        @media #{$md} {
            padding: 20px !important;
        }
    }
    .comparison table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        table-layout: fixed;
    }
    .comparison td, .comparison th {
        text-align: center;
    }
    
    .comparison tbody tr:nth-child(odd) {
        display: none;
    }
    .comparison .compare-row td {
        padding: 25px 0;
        border-top: 1px solid #FAE7E8;
    }
    .comparison tr td:first-child {
        text-align: left;
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s24);
        line-height: var(--ztc-font-size-font-s24);
        font-weight: var(--ztc-weight-bold);
        color: var(--ztc-text-text-3);
        padding: 25px 0;
        border-top: 1px solid #FAE7E8;
    }

    .comparison .tl2 {
    font-family: var(--ztc-family-font1);
    line-height: var(--ztc-font-size-font-s24);
    font-weight: var(--ztc-weight-bold);
    color: var(--ztc-text-text-3);
    font-size: var(--ztc-font-size-font-s24);
    span {
        display: inline-block;
        font-size: var(--ztc-font-size-font-s18);
        line-height: var(--ztc-font-size-font-s18);
        font-weight: var(--ztc-weight-medium);
        color: var(--ztc-text-text-4);
        font-family: var(--ztc-family-font1);
        margin-top: 32px;
        position: absolute;
        left: 6%;
        top: 55px;
        @media #{$md} {
            display: none;
        }
    }
    }

    .comparison .qbo {
    font-family: var(--ztc-family-font1);
    font-size: var(--ztc-font-size-font-s20);
    font-weight: var(--ztc-weight-bold);
    color: var(--ztc-text-text-3);
    line-height: var(--ztc-font-size-font-s20);
    padding: 0 !important;
    }
    
    th.price-info.hide-mobile {
        padding-bottom: 40px;
    }
    .comparison .price-now span {
        font-family: var(--ztc-family-font1);
        font-size: var(--ztc-font-size-font-s44);
        line-height: var(--ztc-font-size-font-s44);
        font-weight: var(--ztc-weight-bold);
        color: var(--ztc-text-text-3);
        transition: all .4s;
        display: inline-block;
        margin-top: 20px;
        margin-bottom: 24px;
    }

    @media #{$xs} {
        .comparison {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s20);
            font-weight: var(--ztc-weight-bold);
            color: var(--ztc-text-text-3);
            transition: all .4s;
            padding: 20px !important;
            background: var(--ztc-text-text-1);
       }
       th.price-info.hide-mobile {
        padding-bottom: 20px;
    }
        .comparison .qbo {
            background: none;
            padding: 10px !important;
       }
        .comparison td:first-child, .comparison th:first-child {
            display: none;
       }
        .comparison tbody tr:nth-child(odd) {
            display: table-row;
       }
        .comparison .row {
            background: #fff;
       }
        .comparison td, .comparison th {
            border: 1px solid #FAE7E8;
            padding: 20px 0;
       }
    }
}
/*============= PRICING CSS AREA ENDS ===============*/

/*============= SLIDER CSS AREA ===============*/
.slider-section-area {
    position: relative;
    z-index: 1;
    background: var(--ztc-bg-bg-1);
    padding: 80px  0;
    @media #{$xs} {
        padding: 40px 0;
    }
    @media #{$md} {
        padding: 40px 0;
    }
    .header-slider {
        margin-bottom: 60px;
        h3 {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s20);
            line-height: var(--ztc-font-size-font-s20);
            color: var(--ztc-text-text-3);
            font-weight: var(--ztc-weight-bold);
            text-transform: capitalize;
        }
    }
    .slider-images {
        .img1 {
            img {
                height: 40px;
                width: 160px;
                object-fit: contain;
            }
        }
    }
}
/*============= SLIDER CSS AREA ENDS ===============*/

/*============= MISSION CSS AREA ===============*/
.mission-section-area {
    position: relative;
    z-index: 1;
    background: var(--ztc-bg-bg-1);
    .mission-header-area {
        margin-bottom: 60px;
        @media #{$xs} {
            margin-bottom: 30px;
        }
        @media #{$md} {
            margin-bottom: 30px;
        }
    }
    .tabs-auhtor-area {
        .nav.nav-pills {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
            .nav-item {
                display: inline-block;
                button {
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s24);
                    line-height: var(--ztc-font-size-font-s24);
                    color: var(--ztc-text-text-10);
                    font-weight: var(--ztc-weight-medium) !important;
                    transition: all .4s;
                    background: var(--ztc-text-text-1);
                    display: inline-block;
                    width: 400px;
                    text-align: center;
                    padding: 20px !important;
                    border-radius: 4px;
                    @media #{$md} {
                        width: 215px;
                    }
                    @media #{$xs} {
                        width: 100%;
                        margin-bottom: 10px;
                    }
                }
            }
            .nav-link.active {
                background: var(--ztc-bg-bg-11);
                color: var(--ztc-text-text-1);
            }
            .nav-link {
                padding: 0 !important;
            }
        }
        .tab-pane {
            .mission-img {
                img {
                    height: 100%;
                    width: 100%;
                    transition: all .4s;
                    border-radius: 4px;
                    object-fit: cover;
                }
            }
            .mission-content-area {
                padding: 0 0 0 75px;
                @media #{$xs} {
                    padding: 0;
                    margin-top: 30px;
                }
                @media #{$md} {
                    padding: 0;
                    margin-top: 30px;
                }
                p{
                    margin-bottom: 16px;
                }
                .btn-area {
                    margin-top: 32px;
                }
            }
        }
        .tab-pane.fade {
            top: 120px;
            position: relative;
            transition: auto;
            transition: all .6s;
            transform: rotateX(50px);
        }
        
        .tab-pane.fade.show{
            top: 0;
            transition: all .6s;
            transform: rotateX(0);
        }
    }
}
/*============= MISSION CSS AREA ENDS ===============*/