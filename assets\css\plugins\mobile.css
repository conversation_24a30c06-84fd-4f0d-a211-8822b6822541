/*

Plugin Name: Multi Drop Down Mobile menu
version: 1.0
Author: <PERSON>: 



*/




/*Base css*/

*{
    margin: 0;
    padding: 0;
}
body{
    font-size: 16px;
    font-family: "Figtree", sans-serif;
}

a, a:hover{
    outline: none;
    text-decoration: none;
}

ul, li{
    list-style: none;
}




/*Menu Css*/

.mobile-logo a{
    font-size: 20px;
    font-weight: 500;
    color: #161616;
    text-decoration: none;
}


.mobile-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    padding: 17px 0;
    z-index: 9;
    
}

.mobile-header-elements {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mobile-nav-icon {
    font-size: 23px;
    border: 1px solid;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    color: #161616;
}



.mobile-sidebar {
    position: fixed;
    height: 100%;
    width: 100%;
    background:#F6F6EC;
    z-index: 99;
    padding: 40px 30px;
    left: -100%;
    top: 0;
    visibility: hidden;
    opacity: 0;
    transition: all .3s;
    
    
}

.mobile-sidebar .contact-icon{
    background: #161616;
}


.mobile-sidebar.mobile-menu-active{
    left: 0;
    visibility: visible;
    opacity: 1;
    transition: all .3s;
    overflow-y:scroll ;
}

.menu-close {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 25px;
    color: #161616;
    cursor: pointer;
}

.mobile-nav li a {
    font-size: 18px;
    line-height: 24px;
    color: #161616;
    padding: 10px 0;
    display: block;
    font-family: "Figtree", sans-serif;
    font-weight: 500 !important;
}


ul.mobile-nav-list {
    margin-top: 30px;
}


li.has-children {
    position: relative;
    z-index: 2;
}

span.mobile-nav-menu-icon {
    position: absolute;
    right: 0;
    top: 0;
    height: 50px;
    width: 50px;
    text-align: center;
    line-height: 50px;
    color: #161616;
}

ul.mobile-menu-sub {
    display: none;
    position: relative;
    left: 0;
    padding-left: 10px;
    transition: all .3s;
}

ul.mobile-menu-sub.sub-menu-active{
    display: block;
    position: relative;
    transition: all .3s;
}



.mobile-nav li {
    position: relative;
    z-index: 2;
}

span.submenu-button {
    position: absolute;
    width: 100%;
    height: 44px;
    top: 0;
    left: 0;
    z-index: 3;
    transition: all .3s;
}

span.submenu-button:after, span.submenu-button:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 2px;
    background: #161616;
    top: 12px;
    right: 9px;
    transition: all .3s;
}

span.submenu-button:before {
    height: 2px;
    width: 20px;
    right: 0px;
    top: 22px;
}

span.submenu-button.submenu-opened:after {
    visibility: hidden;
    opacity: 0;
}

.sub-menu{
    display: none;
    padding-left: 15px;
}


span.multi-drop-icon {
    position: absolute;
    right: 10px;
    width: 20px;
    height: 100%;
    top: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}


ul.mobile-nav-list {
    max-height: 500px;
    overflow-y: scroll;
}