<?php
function custom_template_enqueue_assets() {

    // ======================
    // CSS FILES
    // ======================
    wp_enqueue_style(
        'bootstrap',
        get_template_directory_uri() . '/assets/css/plugins/bootstrap.min.css',
        array(),
        '5.0'
    );

    wp_enqueue_style(
        'fontawesome',
        get_template_directory_uri() . '/assets/css/plugins/fontawesome.css',
        array(),
        '6.0'
    );

    wp_enqueue_style(
        'aos',
        get_template_directory_uri() . '/assets/css/plugins/aos.css',
        array(),
        '3.0'
    );

    wp_enqueue_style(
        'magnific-popup',
        get_template_directory_uri() . '/assets/css/plugins/magnific-popup.css',
        array(),
        '1.0'
    );

    wp_enqueue_style(
        'nice-select',
        get_template_directory_uri() . '/assets/css/plugins/nice-select.css',
        array(),
        '1.0'
    );

    wp_enqueue_style(
        'owlcarousel',
        get_template_directory_uri() . '/assets/css/plugins/owlcarousel.min.css',
        array(),
        '1.0'
    );

    wp_enqueue_style(
        'slick-slider',
        get_template_directory_uri() . '/assets/css/plugins/slick-slider.css',
        array(),
        '1.0'
    );

    wp_enqueue_style(
        'mobile',
        get_template_directory_uri() . '/assets/css/plugins/mobile.css',
        array(),
        '1.0'
    );

    wp_enqueue_style(
        'sidebar',
        get_template_directory_uri() . '/assets/css/plugins/sidebar.css',
        array(),
        '1.0'
    );

    // Main CSS (compiled)
    wp_enqueue_style(
        'custom-main',
        get_template_directory_uri() . '/assets/css/main.css',
        array(),
        filemtime(get_template_directory() . '/assets/css/main.css')
    );


    // ======================
    // JS FILES
    // ======================

    // jQuery (your local version, instead of WP's default)
    wp_deregister_script('jquery');
    wp_enqueue_script(
        'jquery',
        get_template_directory_uri() . '/assets/js/plugins/jquery-3-6-0.min.js',
        array(),
        '3.6.0',
        true
    );

    wp_enqueue_script('bootstrap', get_template_directory_uri() . '/assets/js/plugins/bootstrap.min.js', array('jquery'), '5.0', true);
    wp_enqueue_script('fontawesome', get_template_directory_uri() . '/assets/js/plugins/fontawesome.js', array(), '6.0', true);
    wp_enqueue_script('aos', get_template_directory_uri() . '/assets/js/plugins/aos.js', array('jquery'), '3.0', true);
    wp_enqueue_script('counter', get_template_directory_uri() . '/assets/js/plugins/counter.js', array('jquery'), '1.0', true);
    wp_enqueue_script('gsap', get_template_directory_uri() . '/assets/js/plugins/gsap.min.js', array(), '3.0', true);
    wp_enqueue_script('gsap-animation', get_template_directory_uri() . '/assets/js/plugins/gsap-animation.js', array('gsap'), '1.0', true);
    wp_enqueue_script('scrolltrigger', get_template_directory_uri() . '/assets/js/plugins/ScrollTrigger.min.js', array('gsap'), '3.0', true);
    wp_enqueue_script('magnific-popup', get_template_directory_uri() . '/assets/js/plugins/magnific-popup.js', array('jquery'), '1.0', true);
    wp_enqueue_script('mobilemenu', get_template_directory_uri() . '/assets/js/plugins/mobilemenu.js', array('jquery'), '1.0', true);
    wp_enqueue_script('nice-select', get_template_directory_uri() . '/assets/js/plugins/nice-select.js', array('jquery'), '1.0', true);
    wp_enqueue_script('owlcarousel', get_template_directory_uri() . '/assets/js/plugins/owlcarousel.min.js', array('jquery'), '1.0', true);
    wp_enqueue_script('sidebar', get_template_directory_uri() . '/assets/js/plugins/sidebar.js', array('jquery'), '1.0', true);
    wp_enqueue_script('slick-slider', get_template_directory_uri() . '/assets/js/plugins/slick-slider.js', array('jquery'), '1.0', true);
    wp_enqueue_script('splittext', get_template_directory_uri() . '/assets/js/plugins/Splitetext.js', array('jquery'), '1.0', true);
    wp_enqueue_script('waypoints', get_template_directory_uri() . '/assets/js/plugins/waypoints.js', array('jquery'), '1.0', true);

    // Main JS
    wp_enqueue_script(
        'custom-main',
        get_template_directory_uri() . '/assets/js/main.js',
        array('jquery'),
        filemtime(get_template_directory() . '/assets/js/main.js'),
        true
    );
}
add_action('wp_enqueue_scripts', 'custom_template_enqueue_assets');


// Theme features
function custom_template_setup() {
    // Enable menus
    register_nav_menus(array(
        'main_menu' => 'Main Navigation Menu',
    ));

    // Enable featured images
    add_theme_support('post-thumbnails');

    // Enable dynamic <title> tag
    add_theme_support('title-tag');
}
add_action('after_setup_theme', 'custom_template_setup');