@use '../utils' as *;

/*============= ABOUT CSS AREA ===============*/
.about1-section-area {
    position: relative;
    z-index: 1;
    .about-images-area {
        position: relative;
        .img1 {
            img {
                width: 100%;
                height: 370px;
                object-fit: cover;
                border-radius: 4px;
                @media #{$md} {
                    height: 100%;
                }
                @media #{$xs} {
                    height: 100%;
                    width: 100%;
                }
            }
        }
        .img2 {
            position: relative;
            left: 44%;
            margin-top: -79px;
            @media #{$md} {
                margin-top: -105px;
            }
            @media #{$xs} {
                margin-top: 30px;
                left: 0;
            }
            img {
                width: 362px;
                height: 250px;
                object-fit: cover;
                border-radius: 4px;
                @media #{$md} {
                   height: 270px;
                   width: 385px;
                }
                @media #{$xs} {
                    height: 100%;
                    width: 100%;
                }
            }
        }
        .conter-area {
            background: var(--ztc-text-text-2);
            display: inline-block;
            text-align: center;
            padding: 40px 35px;
            border-radius: 4px;
            position: absolute; 
            bottom: 0;
            width: 267px;
            @media #{$md} {
                width: 290px;
            }
            @media #{$xs} {
                width: 100%;
                height: 100%;
                position: relative;
                margin-top: 30px;
            }
            h3 {
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s44);
                line-height: var(--ztc-font-size-font-s44);
                font-weight: var(--ztc-weight-bold);
                color: var(--ztc-text-text-1);
                margin-bottom: 20px;
            }
            p {
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s16);
                line-height: 16px;
                color: var(--ztc-text-text-1);
                opacity: 0.8;
            }
        }
    }
    .about-header-area {
        padding: 0 0 0 80px;
        @media #{$xs} {
            margin-top: 30px;
            padding: 0;
        }
        @media #{$md} {
            margin-top: 30px;
            padding: 0;
        }

        ul {
            li {
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s18);
                font-weight: var(--ztc-weight-semibold);
                color: var(--ztc-text-text-3);
                line-height: var(--ztc-font-size-font-s18);
                margin-top: 16px;
                display: flex;
                img {
                    margin: 0 8px 0 0;
                }
            }
        }
        .btn-area {
            margin-top: 32px;
        }
    }
}

// homepage2 //
.about2-section-area {
    .about-images-area {
        position: relative;
        &:hover {
            .img1 {
                &::after {
                    left: 0;
                    top: 0;
                    transition: all .4s;
                }
            }
        }
        .img1 {
            position: relative;
            z-index: 1;
            transition: all .4s;
            &::after {
                position: absolute;
                content: "";
                height: 100%;
                left: 30px;
                top: 30px;
                width: 100%;
                transition: all .4s;
                background: var(--ztc-bg-bg-3);
                border-radius: 4px;
                z-index: -1;
            }
            img {
                height: 100%;
                width: 100%;
                border-radius: 4px;
            }
        }
        .img2 {
            position: absolute;
            right: -50px;
            bottom: -50px;
            z-index: 1;
            background: var(--ztc-text-text-1);
            border-radius: 4px;
            padding: 10px;
            @media #{$md} {
                right: -30px;
            }
            @media #{$xs} {
                right: 0;
            }
            img {
                height: 200px;
                width: 240px;
                object-fit: cover;
                border-radius: 4px;
            }
        }
    }

    .about-header-area {
        padding: 0 0 0 80px;
        @media #{$xs} {
            padding: 0;
            margin-top: 60px;
        }
        @media #{$md} {
            padding: 0;
            margin-top: 60px;
        }
        .progress_bar {
            .progress_bar_item {
                margin-bottom: 20px;
                position: relative;
            }
            .item_label,
            .item_value {
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s20);
                line-height: var(--ztc-font-size-font-s20);
                font-weight: var(--ztc-weight-bold);
                color: var(--ztc-text-text-5);
            }
            .item_value.cell1 {
                position: absolute;
                right: 80px;
                top: 0;
                @media #{$xs} {
                    right: 0;
                }
            }
            .item_value.cell2 {
                position: absolute;
                right: 35px;
                top: 0;
                @media #{$xs} {
                    right: 0;
                }
            }
            .item_value.cell3 {
                position: absolute;
                right: 140px;
                top: 0;
                @media #{$xs} {
                    right: 0;
                }
            }
            .item_bar {
                position: relative;
                height: 8px;
                width: 100%;
                background-color: var(--ztc-bg-bg-6);
                border-radius: 4px;
                margin-top: 16px;
                .progress {
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    width: 0;
                    height: 8px;
                    margin: 0;
                    background-color: var(--ztc-text-text-5);
                    border-radius: 4px;
                    transition: width 100ms ease;
                }
            }
        }

        .header-btn3 {
            margin-top: 12px;
        }
    }
}

// homepage3 // 
.about3-section-area {
    position: relative;
    z-index: 1;
    .about3-header-area {
        .misson-text {
            margin-top: 24px;
            p {
                font-size: var(--ztc-font-size-font-s24);
                font-family: var(--ztc-family-font1);
                font-weight: var(--ztc-weight-bold);
                color: var(--ztc-text-text-7);
                line-height: var(--ztc-font-size-font-s24);
            }
            ul {
                li {
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s18);
                    line-height: var(--ztc-font-size-font-s18);
                    color: var(--ztc-text-text-8);
                    font-weight: var(--ztc-weight-semibold);
                    margin-top: 16px;
                    img {
                        margin: 0 8px 0 0;
                    }
                }
            }
        }
        .btn-area {
            margin-top: 32px;
        }
    }
    .about-images-area {
        position: relative;
        .img1 {
            img {
                height: 100%;
                width: 100%;
                border-radius: 4px;
            }
            .about-footer-bottom {
                display: flex;
                align-items: center;
                background: var(--ztc-bg-bg-7);
                border-radius: 4px;
                padding: 32px 24px;
                margin-top: 32px;
                .content {
                    margin-left: 16px;
                    span {
                        font-family: var(--ztc-family-font1);
                        font-size: var(--ztc-font-size-font-s20);
                        line-height: var(--ztc-font-size-font-s24);
                        color: var(--ztc-text-text-1);
                        text-transform: capitalize;
                        font-weight: var(--ztc-weight-bold);
                        display: inline-block;
                    }
                }
            }
        }
    }
}

// homepage4 //
.about4-section-area {
    .about-header-area {
        padding: 0 82px 0 0;
        @media #{$xs} {
            margin-bottom: 30px;
            padding: 0;
        }
        @media #{$md} {
            margin-bottom: 30px;
            padding: 0;
        }
        .counter-box {
            background: var(--ztc-bg-bg-14);
            text-align: center;
            margin-top: 30px;
            padding: 24px;
            border-radius: 4px;
            transition: all .4s;
            &:hover {
                background: var(--ztc-bg-bg-11);
                transition: all .4s;
                transform: translateY(-5px);
                h2 {
                    color: var(--ztc-text-text-1);
                    transition: all .4s;
                }
                p {
                    color: var(--ztc-text-text-1);
                    transition: all .4s;
                }
            }
            h2 {
                line-height: 44px;
                transition: all .4s;
            }
            p {
                line-height: 16px;
                transition: all .4s;
            }
        }
        .btn-area {
            margin-top: 32px;
        }
    }

    .about-images-area {
        position: relative;
        z-index: 1;
        .img1 {
            position: relative;
            .elements2 {
                position: absolute;
                top: -43px;
                right: -24px;
                height: 100px;
                width: 100px;
                object-fit: contain;
            }
            img {
                height: 415px;
                width: 100%;
                border-radius: 4px;
                transition: all 0.4s;
                -o-object-fit: cover;
                object-fit: cover;
                @media #{$xs} {
                    width: 100%;
                    height: 100%;

                }
            
            }
        }
        .img2 {
            position: absolute;
            right: 76px;
            bottom: 0px;
            @media #{$md} {
                right: 82px;
                bottom: -10px;
            }
            @media #{$xs} {
                position: relative;
                right: 0;
                left: 0;
            }
            img {
                height: 282px;
                width: 282px;
                border-radius: 4px;
                object-fit: cover;
                transition: all .4s;
                @media #{$md} {
                    width: 310px;;
                }
                @media #{$xs} {
                    width: 100%;
                    margin-top: 20px;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }
        .content-experiance {
            background: var(--ztc-bg-bg-11);
            border-radius: 4px;
            display: inline-block;
            padding: 32px 40px;
            transition: all .4s;
            text-align: center;
            margin-top: 8px;
            width: 268px;

            @media #{$md} {
                width: 290px;
            }
            @media #{$xs} {
                width: 100%;
                margin-top: 30px;
            }
            h3 {
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s24);
                line-height: var(--ztc-font-size-font-s24);
                color: var(--ztc-text-text-1);
                font-weight: var(--ztc-weight-bold);
                transition: all .4s;
                margin-bottom: 16px;
            }
            h2 {
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s44);
                line-height: var(--ztc-font-size-font-s44);
                color: var(--ztc-text-text-1);
                transition: all .4s;
                font-weight: var(--ztc-weight-bold);
                margin-bottom: 8px;
            }
            p {
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s16);
                line-height: var(--ztc-font-size-font-s16);
                font-weight: var(--ztc-weight-medium);
                color: var(--ztc-text-text-1);
                opacity: 80%;
                transition: all .4s;
            }
        }
    }
}

// about-innerpages //
.hero1-section-area.about-bg-area {
    position: relative;
    padding: 250px 0 130px !important;
    @media #{$xs} {
        padding: 150px 0 100px !important;
    }
    .header-img1 {
        width: 100% !important;
    }
    .hero-heading-area {
        h1 {
            margin-bottom: 0;
        }
        .backline {
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            line-height: var(--ztc-font-size-font-s18);
            font-weight: var(--ztc-weight-medium);
            color: var(--ztc-text-text-1);
            transition: all .4s;
            display: inline-block;
            opacity: 90%;
            margin-top: 16px;
            span {
                font-weight: var(--ztc-weight-bold);
                line-height: var(--ztc-font-size-font-s24);
            }
            i {
                margin: 0 4px;
            }
        }
    }
}

.aboutpage-inner {
    background: var(--ztc-bg-bg-1) !important;
    position: relative;
    .about3-header-area {
        .misson-text {
            p {
                font-weight: var(--ztc-weight-bold);
                color: var(--ztc-text-text-3);
            }
            ul {
                li {
                    font-weight: var(--ztc-weight-semibold);
                    color: var(--ztc-text-text-3);
                }
            }
        }
    }
    .about-images-area {
        .about-footer-bottom {
            background: var(--ztc-text-text-2) !important;
        }
    }
}


.about-innerpage {
    .about-images-area {
        .img1 {
            &::after {
                background: var(--ztc-text-text-2);
            }
        }
    }
    .about-header-area {
        .progress_bar_item {
            .item_label {
                color: var(--ztc-text-text-3);
                font-weight: var(--ztc-weight-bold);
            }
            .item-value {
                color: var(--ztc-text-text-3);
            }
            .item-bar {
                .progress {
                    background: var(--ztc-text-text-3);
                }
            }
        }
        .btn-area {
            margin-top: 32px;
        }
    }
}
/*============= ABOUT CSS AREA ENDS ===============*/