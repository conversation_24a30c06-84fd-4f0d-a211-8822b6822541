@import url("https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&family=Marcellus+SC&family=Outfit:wght@100..900&family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap");
body, html {
  overflow-x: hidden;
}

a, a:hover {
  text-decoration: none;
}

ul {
  padding: 0;
  margin: 0;
}
ul li {
  list-style: none;
}

h2, p {
  margin-bottom: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: 0;
}

input, textarea {
  background: none;
  border: none;
  outline: none;
}

img {
  max-width: 100%;
  max-height: 100%;
  transition: all 0.4s ease-in-out;
}

.space6 {
  height: 6px;
}

.space8 {
  height: 8px;
}

.space10 {
  height: 10px;
}

.space12 {
  height: 12px;
}

.space14 {
  height: 14px;
}

.space16 {
  height: 16px;
}

.space18 {
  height: 18px;
}

.space20 {
  height: 20px;
}

.space22 {
  height: 22px;
}

.space24 {
  height: 24px;
}

.space26 {
  height: 26px;
}

.space28 {
  height: 28px;
}

.space30 {
  height: 30px;
}

.space32 {
  height: 32px;
}

.space34 {
  height: 34px;
}

.space36 {
  height: 36px;
}

.space38 {
  height: 38px;
}

.space40 {
  height: 40px;
}

.space42 {
  height: 42px;
}

.space44 {
  height: 44px;
}

.space46 {
  height: 46px;
}

.space48 {
  height: 48px;
}

.space50 {
  height: 50px;
}

.space52 {
  height: 52px;
}

.space54 {
  height: 54px;
}

.space56 {
  height: 56px;
}

.space58 {
  height: 58px;
}

.space60 {
  height: 60px;
}

.space62 {
  height: 62px;
}

.space64 {
  height: 64px;
}

.space66 {
  height: 66px;
}

.space68 {
  height: 68px;
}

.space70 {
  height: 70px;
}

.space72 {
  height: 72px;
}

.space74 {
  height: 74px;
}

.space76 {
  height: 76px;
}

.space78 {
  height: 78px;
}

.space80 {
  height: 80px;
}

.space82 {
  height: 82px;
}

.space84 {
  height: 84px;
}

.space86 {
  height: 86px;
}

.space {
  height: 88px;
}

.space90 {
  height: 90px;
}

.space92 {
  height: 92px;
}

.space94 {
  height: 94px;
}

.space96 {
  height: 96px;
}

.space98 {
  height: 98px;
}

.space100 {
  height: 100px;
}

@keyframes animation-5 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}
.aniamtion-key-5 {
  position: relative;
  animation-name: animation-5;
  animation-duration: 1s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  transition: all 0.4s ease-in-out;
}

@keyframes animation-7 {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(-1000deg);
  }
}
.keyframe5 {
  position: relative;
  animation-name: animation-7;
  animation-duration: 40s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: cubic-bezier(0.59, 0.59, 1, 1);
}

@keyframes animation-1 {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(30px);
  }
}
.aniamtion-key-1 {
  position: relative;
  animation-name: animation-1;
  animation-duration: 1.3s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}

@keyframes animation-2 {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(50px);
  }
}
.aniamtion-key-2 {
  position: relative;
  animation-name: animation-2;
  animation-duration: 1.3s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}

@keyframes animation-3 {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(60px);
  }
}
.aniamtion-key-3 {
  position: relative;
  animation-name: animation-3;
  animation-duration: 1.3s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}

@keyframes animation-4 {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(40px);
  }
}
.aniamtion-key-4 {
  position: relative;
  animation-name: animation-4;
  animation-duration: 1.6s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}

@keyframes animation-6 {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(50px);
  }
}
.aniamtion-key-6 {
  position: relative;
  animation-name: animation-6;
  animation-duration: 1.9s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}

@keyframes animation-8 {
  0% {
    padding-left: 0px;
  }
  100% {
    padding-left: 50px;
  }
}
.aniamtion-key-7 {
  position: relative;
  animation-name: animation-8;
  animation-duration: 1.3s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}
@keyframes fade-in-down {
  0% {
    transform: translate3d(0, -50px, 0);
  }
  100% {
    opacity: 1;
    transform: none;
  }
}
@keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}
@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translatex(-100%);
  }
}
@keyframes marquee-2 {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translatex(0);
  }
}
:root {
  --ztc-text-text-1: #fff;
  --ztc-text-text-2: #C90F16;
  --ztc-text-text-3: #060404;
  --ztc-text-text-4: #5A5454;
  --ztc-text-text-5: #142637;
  --ztc-text-text-6: #525455;
  --ztc-text-text-7: #0A0400;
  --ztc-text-text-8: #5F5C5A;
  --ztc-text-text-9: #EC631A;
  --ztc-text-text-10: #01060E;
  --ztc-text-text-11: #5A5A5A;
  --ztc-bg-bg-1: #F3F5F7;
  --ztc-bg-bg-2: #FAE7E8;
  --ztc-bg-bg-3: #FFD028;
  --ztc-bg-bg-4: #2C3C4B;
  --ztc-bg-bg-5: #E7E9EB;
  --ztc-bg-bg-6: #B9BEC3;
  --ztc-bg-bg-7: #EE6400;
  --ztc-bg-bg-8: #F3E7DE;
  --ztc-bg-bg-10: #FDF0E5;
  --ztc-bg-bg-11: #0145AC;
  --ztc-bg-bg-12: #DBE4F3;
  --ztc-bg-bg-13: #E6ECF7;
  --ztc-bg-bg-14: #E7ECF3;
  --ztc-border-border-1: #f0f0f0;
  --ztc-border-border-2: #dfdcdc;
  --ztc-font-size-font-s10: 10px;
  --ztc-font-size-font-s12: 12px;
  --ztc-font-size-font-s14: 14px;
  --ztc-font-size-font-s16: 16px;
  --ztc-font-size-font-s18: 18px;
  --ztc-font-size-font-s20: 20px;
  --ztc-font-size-font-s22: 22px;
  --ztc-font-size-font-s24: 24px;
  --ztc-font-size-font-s26: 26px;
  --ztc-font-size-font-s28: 28px;
  --ztc-font-size-font-s30: 30px;
  --ztc-font-size-font-s32: 32px;
  --ztc-font-size-font-s34: 34px;
  --ztc-font-size-font-s36: 36px;
  --ztc-font-size-font-s38: 38px;
  --ztc-font-size-font-s40: 40px;
  --ztc-font-size-font-s42: 42px;
  --ztc-font-size-font-s44: 44px;
  --ztc-font-size-font-s46: 46px;
  --ztc-font-size-font-s48: 48px;
  --ztc-font-size-font-s50: 50px;
  --ztc-font-size-font-s52: 52px;
  --ztc-font-size-font-s54: 54px;
  --ztc-font-size-font-s56: 56px;
  --ztc-font-size-font-s58: 58px;
  --ztc-font-size-font-s60: 60px;
  --ztc-font-size-font-s62: 62px;
  --ztc-font-size-font-s64: 64px;
  --ztc-font-size-font-s66: 66px;
  --ztc-font-size-font-s68: 68px;
  --ztc-font-size-font-s70: 70px;
  --ztc-font-size-font-s10: 10px;
  --ztc-font-size-font-s12: 12px;
  --ztc-font-size-font-s14: 14px;
  --ztc-font-size-font-s16: 16px;
  --ztc-font-size-font-s18: 18px;
  --ztc-font-size-font-s20: 20px;
  --ztc-font-size-font-s22: 22px;
  --ztc-font-size-font-s24: 24px;
  --ztc-font-size-font-s26: 26px;
  --ztc-font-size-font-s28: 28px;
  --ztc-font-size-font-s30: 30px;
  --ztc-font-size-font-s32: 32px;
  --ztc-font-size-font-s34: 34px;
  --ztc-font-size-font-s36: 36px;
  --ztc-font-size-font-s38: 38px;
  --ztc-font-size-font-s40: 40px;
  --ztc-font-size-font-s42: 42px;
  --ztc-font-size-font-s44: 44px;
  --ztc-font-size-font-s46: 46px;
  --ztc-font-size-font-s48: 48px;
  --ztc-font-size-font-s50: 50px;
  --ztc-font-size-font-s52: 52px;
  --ztc-font-size-font-s54: 54px;
  --ztc-font-size-font-s56: 56px;
  --ztc-font-size-font-s58: 58px;
  --ztc-font-size-font-s60: 60px;
  --ztc-font-size-font-s62: 62px;
  --ztc-font-size-font-s64: 64px;
  --ztc-font-size-font-s66: 66px;
  --ztc-font-size-font-s68: 68px;
  --ztc-font-size-font-s70: 70px;
  --ztc-specing-height6: 6px;
  --ztc-specing-height8: 8px;
  --ztc-specing-height10: 10px;
  --ztc-specing-height12: 12px;
  --ztc-specing-height14: 114px;
  --ztc-specing-height16: 16px;
  --ztc-specing-height18: 18px;
  --ztc-specing-height20: 20px;
  --ztc-specing-height22: 22px;
  --ztc-specing-height24: 24px;
  --ztc-specing-height26: 26px;
  --ztc-specing-height28: 28px;
  --ztc-specing-height30: 30px;
  --ztc-specing-height32: 32px;
  --ztc-specing-height34: 34px;
  --ztc-specing-height36: 36px;
  --ztc-specing-height38: 38px;
  --ztc-specing-height40: 40px;
  --ztc-specing-height42: 42px;
  --ztc-specing-height44: 44px;
  --ztc-specing-height46: 46px;
  --ztc-specing-height48: 48px;
  --ztc-specing-height50: 50px;
  --ztc-specing-height52: 52px;
  --ztc-specing-height54: 54px;
  --ztc-specing-height56: 56px;
  --ztc-specing-height58: 58px;
  --ztc-specing-height60: 60px;
  --ztc-specing-height70: 70px;
  --ztc-specing-height80: 80px;
  --ztc-specing-height90: 90px;
  --ztc-specing-height100: 100px;
  --ztc-specing-height110: 110px;
  --ztc-specing-height120: 120px;
  --ztc-specing-height130: 130px;
  --ztc-weight-regular: 400;
  --ztc-weight-medium: 500;
  --ztc-weight-semibold: 600;
  --ztc-weight-bold: 700;
  --ztc-weight-black: 800;
  --ztc-family-font1: 'Figtree', sans-serif;
}

/*
============================
Name:  Current - Electricity Services Templete
Version: 1.0.0
Description: Current - Electricity Services Templete
Author: Vikiglab
Author URI: https://themeforest.net/user/vikinglab/portfolio
Location:
============================
*/
/*============= HEDAER CSS AREA ===============*/
/*============= HERO CSS AREA ===============*/
/*============= MOBILE-MENU CSS AREA ===============*/
/*============= ABOUT CSS AREA ===============*/
/*============= SERVICE CSS AREA ===============*/
/*============= WORK CSS AREA ===============*/
/*============= FEATURES CSS AREA ===============*/
/*============= TESTIMONIAL CSS AREA ===============*/
/*============= BLOG CSS AREA ===============*/
/*============= CONTACT CSS AREA ===============*/
/*============= TEAM CSS AREA ===============*/
/*============= FAQ CSS AREA ===============*/
/*============= CASE CSS AREA ===============*/
/*============= FOOTER CSS AREA ===============*/
/*============= COMMON CSS AREA ===============*/
.heading1 h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  border-radius: 4px;
  padding: 8px 12px;
  text-transform: capitalize;
  display: inline-block;
  position: relative;
  z-index: 1;
  margin-bottom: 20px;
}
.heading1 h5::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  opacity: 10%;
  z-index: -1;
}
.heading1 h1 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s64);
  line-height: var(--ztc-font-size-font-s70);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-1);
  text-transform: capitalize;
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .heading1 h1 {
    font-size: var(--ztc-font-size-font-s32);
    line-height: var(--ztc-font-size-font-s42);
  }
}
.heading1 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s26);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 0.9 !important;
  margin-bottom: 10px;
}

.heading2 h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-2);
  border-radius: 4px;
  padding: 8px 12px;
  text-transform: capitalize;
  display: inline-block;
  position: relative;
  z-index: 1;
  margin-bottom: 20px;
}
.heading2 h5::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  border-radius: 4px;
  background: var(--ztc-bg-bg-2);
  z-index: -1;
}
.heading2 h2 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s44);
  line-height: var(--ztc-font-size-font-s54);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .heading2 h2 {
    font-size: var(--ztc-font-size-font-s32);
    line-height: var(--ztc-font-size-font-s42);
  }
}
.heading2 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s26);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
  margin-bottom: 10px;
  opacity: 80% !important;
}

.heading3 h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  border-radius: 4px;
  display: inline-block;
  background: var(--ztc-bg-bg-4);
  padding: 8px 12px;
  margin-bottom: 24px;
}
.heading3 h1 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s64);
  line-height: var(--ztc-font-size-font-s70);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-1);
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .heading3 h1 {
    font-size: var(--ztc-font-size-font-s32);
    line-height: var(--ztc-font-size-font-s42);
  }
}
.heading3 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80% !important;
  line-height: var(--ztc-font-size-font-s26);
}

.heading4 h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-5);
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  border-radius: 4px;
  display: inline-block;
  background: var(--ztc-bg-bg-5);
  padding: 8px 12px;
  margin-bottom: 16px;
}
.heading4 h2 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s44);
  line-height: var(--ztc-font-size-font-s54);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-5);
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .heading4 h2 {
    font-size: var(--ztc-font-size-font-s32);
    line-height: var(--ztc-font-size-font-s42);
  }
}
.heading4 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-6);
  line-height: var(--ztc-font-size-font-s26);
}

.heading5 h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-bg-bg-7);
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  border-radius: 4px;
  display: inline-block;
  background: var(--ztc-bg-bg-8);
  padding: 8px 12px;
  margin-bottom: 24px;
}
.heading5 h1 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s64);
  line-height: var(--ztc-font-size-font-s70);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-7);
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .heading5 h1 {
    font-size: var(--ztc-font-size-font-s32);
    line-height: var(--ztc-font-size-font-s42);
  }
}
.heading5 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-8);
  opacity: 80% !important;
  line-height: var(--ztc-font-size-font-s26);
}

.heading6 h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-bg-bg-7);
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  border-radius: 4px;
  display: inline-block;
  background: var(--ztc-bg-bg-10);
  padding: 8px 12px;
  margin-bottom: 16px;
}
.heading6 h2 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s44);
  line-height: var(--ztc-font-size-font-s54);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-7);
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .heading6 h2 {
    font-size: var(--ztc-font-size-font-s32);
    line-height: var(--ztc-font-size-font-s42);
  }
}
.heading6 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-8);
  opacity: 80% !important;
  line-height: var(--ztc-font-size-font-s26);
}

.heading7 h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  border-radius: 4px;
  display: inline-block;
  padding: 8px 12px;
  margin-bottom: 16px;
  position: relative;
}
.heading7 h5::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  opacity: 10%;
  z-index: -1;
}
.heading7 h2 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s44);
  line-height: var(--ztc-font-size-font-s54);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-1);
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .heading7 h2 {
    font-size: var(--ztc-font-size-font-s32);
    line-height: var(--ztc-font-size-font-s42);
  }
}
.heading7 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80% !important;
  line-height: var(--ztc-font-size-font-s26);
}

.heading8 h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-bg-bg-11);
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  border-radius: 4px;
  display: inline-block;
  background: var(--ztc-bg-bg-12);
  padding: 8px 12px;
  margin-bottom: 16px;
}
.heading8 h1 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s64);
  line-height: var(--ztc-font-size-font-s70);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-7);
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .heading8 h1 {
    font-size: var(--ztc-font-size-font-s32);
    line-height: var(--ztc-font-size-font-s42);
  }
}
.heading8 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-8);
  opacity: 80% !important;
  line-height: var(--ztc-font-size-font-s26);
}

.heading9 h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-bg-bg-11);
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  border-radius: 4px;
  display: inline-block;
  background: var(--ztc-bg-bg-12);
  padding: 8px 12px;
  margin-bottom: 16px;
}
.heading9 h2 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s44);
  line-height: var(--ztc-font-size-font-s54);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-10);
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .heading9 h2 {
    font-size: var(--ztc-font-size-font-s32);
    line-height: var(--ztc-font-size-font-s42);
  }
}
.heading9 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-11);
  line-height: var(--ztc-font-size-font-s26);
}

.heading10 h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-bg-bg-11);
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  border-radius: 4px;
  display: inline-block;
  background: var(--ztc-bg-bg-12);
  padding: 8px 12px;
  margin-bottom: 16px;
}
.heading10 h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s32);
  line-height: var(--ztc-font-size-font-s40);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-10);
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .heading10 h3 {
    font-size: var(--ztc-font-size-font-s32);
    line-height: var(--ztc-font-size-font-s42);
  }
}
.heading10 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-11);
  line-height: var(--ztc-font-size-font-s26);
}

.heading11 h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  border-radius: 4px;
  display: inline-block;
  background: #1A1F26;
  padding: 8px 12px;
  margin-bottom: 16px;
}
.heading11 h2 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s44);
  line-height: var(--ztc-font-size-font-s54);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-1);
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .heading11 h2 {
    font-size: var(--ztc-font-size-font-s32);
    line-height: var(--ztc-font-size-font-s42);
  }
}
.heading11 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  line-height: var(--ztc-font-size-font-s26);
  opacity: 80% !important;
}

.header-btn1 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  background: var(--ztc-text-text-2);
  padding: 16px 20px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.header-btn1:hover {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.header-btn1:hover::after {
  width: 100%;
  transition: all 0.4s;
  right: auto;
  left: 0;
}
.header-btn1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 0;
  right: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-1);
  opacity: 0.2;
  z-index: -1;
  border-radius: 4px;
}
.header-btn1 i {
  margin-left: 4px;
  transform: rotate(-45deg);
}

.header-btn2 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  padding: 14px 20px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
  border: 1px solid var(--ztc-text-text-1);
}
.header-btn2:hover {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: translateY(-5px);
  border: 1px solid var(--ztc-text-text-2);
}
.header-btn2:hover::after {
  width: 100%;
  transition: all 0.4s;
  right: auto;
  left: 0;
}
.header-btn2::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 0;
  right: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-2);
  z-index: -1;
  border-radius: 4px;
}
.header-btn2 i {
  margin-left: 4px;
  transform: rotate(-45deg);
}

.header-btn3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-5);
  background: var(--ztc-bg-bg-3);
  padding: 16px 20px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.header-btn3:hover {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.header-btn3:hover::after {
  left: 0;
  top: 0;
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
  border-radius: 4px;
  width: 100%;
  height: 100%;
}
.header-btn3::after {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  border-radius: 50%;
  left: 45%;
  top: 27%;
  transition: all 0.4s;
  background: var(--ztc-text-text-5);
  z-index: -1;
  visibility: hidden;
  opacity: 0;
}
.header-btn3 i {
  margin-left: 4px;
  transform: rotate(-45deg);
}

.header-btn4 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  padding: 14px 20px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
  border: 1px solid var(--ztc-text-text-1);
}
.header-btn4:hover {
  color: var(--ztc-text-text-5);
  transition: all 0.4s;
}
.header-btn4:hover::after {
  left: 0;
  top: 0;
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
  border-radius: 4px;
  width: 100%;
  height: 100%;
}
.header-btn4::after {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  border-radius: 50%;
  left: 45%;
  top: 27%;
  transition: all 0.4s;
  background: var(--ztc-text-text-1);
  z-index: -1;
  visibility: hidden;
  opacity: 0;
}
.header-btn4 i {
  margin-left: 4px;
  transform: rotate(-45deg);
}

.header-btn5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  background: var(--ztc-bg-bg-7);
  padding: 16px 20px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.header-btn5:hover {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.header-btn5:hover::after {
  left: 0;
  top: 0;
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
  border-radius: 4px;
  width: 100%;
  height: 100%;
}
.header-btn5::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 1px;
  border-radius: 4px;
  left: 45%;
  top: 0;
  transition: all 0.4s;
  background: #ff6a00;
  z-index: -1;
  visibility: hidden;
  opacity: 0;
}
.header-btn5 i {
  margin-left: 4px;
  transform: rotate(-45deg);
}

.header-btn6 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-7);
  padding: 14px 20px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
  border: 1px solid var(--ztc-text-text-7);
}
.header-btn6:hover {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  border: 1px solid #ff6a00;
}
.header-btn6:hover::after {
  left: 0;
  top: 0;
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
  border-radius: 4px;
  width: 100%;
  height: 100%;
}
.header-btn6::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 1px;
  border-radius: 4px;
  left: 45%;
  top: 0;
  transition: all 0.4s;
  background: #ff6a00;
  z-index: -1;
  visibility: hidden;
  opacity: 0;
}
.header-btn6 i {
  margin-left: 4px;
  transform: rotate(-45deg);
}

.header-btn7 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  background: var(--ztc-bg-bg-11);
  padding: 16px 20px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.header-btn7:hover {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.header-btn7:hover::after {
  left: 0;
  top: 0;
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
  border-radius: 4px;
  width: 100%;
  height: 100%;
}
.header-btn7::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 1px;
  border-radius: 4px;
  left: 45%;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-7);
  z-index: -1;
  visibility: hidden;
  opacity: 0;
}
.header-btn7 i {
  margin-left: 4px;
  transform: rotate(-45deg);
}

.header-btn8 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-bg-bg-11);
  padding: 16px 20px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
  background: var(--ztc-text-text-1);
}
.header-btn8:hover {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.header-btn8:hover::after {
  left: 0;
  top: 0;
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
  border-radius: 4px;
  width: 100%;
  height: 100%;
}
.header-btn8::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 1px;
  border-radius: 4px;
  left: 45%;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-7);
  z-index: -1;
  visibility: hidden;
  opacity: 0;
}
.header-btn8 i {
  margin-left: 4px;
  transform: rotate(-45deg);
}

/*============= COMMON CSS AREA ENDS===============*/
.sp1 {
  padding: 100px 0 100px;
}
@media (max-width: 767px) {
  .sp1 {
    padding: 50px 0 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .sp1 {
    padding: 50px 0 50px;
  }
}

.sp2 {
  padding: 100px 0 70px;
}
@media (max-width: 767px) {
  .sp2 {
    padding: 50px 0 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .sp2 {
    padding: 50px 0 20px;
  }
}

.sp3 {
  padding: 100px 0 50px;
}

.sp4 {
  padding: 80px 0 80px;
}
@media (max-width: 767px) {
  .sp4 {
    padding: 40px 0 40px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .sp4 {
    padding: 40px 0 40px;
  }
}

.sp5 {
  padding: 70px 0 70px;
}
@media (max-width: 767px) {
  .sp5 {
    padding: 40px 0 40px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .sp5 {
    padding: 40px 0 40px;
  }
}

.sp6 {
  padding: 120px 0 120px;
}
@media (max-width: 767px) {
  .sp6 {
    padding: 60px 0 60px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .sp6 {
    padding: 60px 0 60px;
  }
}

.sp7 {
  padding: 120px 0 90px;
}
@media (max-width: 767px) {
  .sp7 {
    padding: 60px 0 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .sp7 {
    padding: 60px 0 30px;
  }
}

.sp8 {
  padding: 100px 0 0;
}
@media (max-width: 767px) {
  .sp8 {
    padding: 50px 0 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .sp8 {
    padding: 50px 0 0;
  }
}

/*============= ABOUT CSS AREA ===============*/
.about1-section-area {
  position: relative;
  z-index: 1;
}
.about1-section-area .about-images-area {
  position: relative;
}
.about1-section-area .about-images-area .img1 img {
  width: 100%;
  height: 370px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 4px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about1-section-area .about-images-area .img1 img {
    height: 100%;
  }
}
@media (max-width: 767px) {
  .about1-section-area .about-images-area .img1 img {
    height: 100%;
    width: 100%;
  }
}
.about1-section-area .about-images-area .img2 {
  position: relative;
  left: 44%;
  margin-top: -79px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about1-section-area .about-images-area .img2 {
    margin-top: -105px;
  }
}
@media (max-width: 767px) {
  .about1-section-area .about-images-area .img2 {
    margin-top: 30px;
    left: 0;
  }
}
.about1-section-area .about-images-area .img2 img {
  width: 362px;
  height: 250px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 4px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about1-section-area .about-images-area .img2 img {
    height: 270px;
    width: 385px;
  }
}
@media (max-width: 767px) {
  .about1-section-area .about-images-area .img2 img {
    height: 100%;
    width: 100%;
  }
}
.about1-section-area .about-images-area .conter-area {
  background: var(--ztc-text-text-2);
  display: inline-block;
  text-align: center;
  padding: 40px 35px;
  border-radius: 4px;
  position: absolute;
  bottom: 0;
  width: 267px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about1-section-area .about-images-area .conter-area {
    width: 290px;
  }
}
@media (max-width: 767px) {
  .about1-section-area .about-images-area .conter-area {
    width: 100%;
    height: 100%;
    position: relative;
    margin-top: 30px;
  }
}
.about1-section-area .about-images-area .conter-area h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s44);
  line-height: var(--ztc-font-size-font-s44);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-1);
  margin-bottom: 20px;
}
.about1-section-area .about-images-area .conter-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: 16px;
  color: var(--ztc-text-text-1);
  opacity: 0.8;
}
.about1-section-area .about-header-area {
  padding: 0 0 0 80px;
}
@media (max-width: 767px) {
  .about1-section-area .about-header-area {
    margin-top: 30px;
    padding: 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about1-section-area .about-header-area {
    margin-top: 30px;
    padding: 0;
  }
}
.about1-section-area .about-header-area ul li {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-3);
  line-height: var(--ztc-font-size-font-s18);
  margin-top: 16px;
  display: flex;
}
.about1-section-area .about-header-area ul li img {
  margin: 0 8px 0 0;
}
.about1-section-area .about-header-area .btn-area {
  margin-top: 32px;
}

.about2-section-area .about-images-area {
  position: relative;
}
.about2-section-area .about-images-area:hover .img1::after {
  left: 0;
  top: 0;
  transition: all 0.4s;
}
.about2-section-area .about-images-area .img1 {
  position: relative;
  z-index: 1;
  transition: all 0.4s;
}
.about2-section-area .about-images-area .img1::after {
  position: absolute;
  content: "";
  height: 100%;
  left: 30px;
  top: 30px;
  width: 100%;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-3);
  border-radius: 4px;
  z-index: -1;
}
.about2-section-area .about-images-area .img1 img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
}
.about2-section-area .about-images-area .img2 {
  position: absolute;
  right: -50px;
  bottom: -50px;
  z-index: 1;
  background: var(--ztc-text-text-1);
  border-radius: 4px;
  padding: 10px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about2-section-area .about-images-area .img2 {
    right: -30px;
  }
}
@media (max-width: 767px) {
  .about2-section-area .about-images-area .img2 {
    right: 0;
  }
}
.about2-section-area .about-images-area .img2 img {
  height: 200px;
  width: 240px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 4px;
}
.about2-section-area .about-header-area {
  padding: 0 0 0 80px;
}
@media (max-width: 767px) {
  .about2-section-area .about-header-area {
    padding: 0;
    margin-top: 60px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about2-section-area .about-header-area {
    padding: 0;
    margin-top: 60px;
  }
}
.about2-section-area .about-header-area .progress_bar .progress_bar_item {
  margin-bottom: 20px;
  position: relative;
}
.about2-section-area .about-header-area .progress_bar .item_label,
.about2-section-area .about-header-area .progress_bar .item_value {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s20);
  line-height: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-5);
}
.about2-section-area .about-header-area .progress_bar .item_value.cell1 {
  position: absolute;
  right: 80px;
  top: 0;
}
@media (max-width: 767px) {
  .about2-section-area .about-header-area .progress_bar .item_value.cell1 {
    right: 0;
  }
}
.about2-section-area .about-header-area .progress_bar .item_value.cell2 {
  position: absolute;
  right: 35px;
  top: 0;
}
@media (max-width: 767px) {
  .about2-section-area .about-header-area .progress_bar .item_value.cell2 {
    right: 0;
  }
}
.about2-section-area .about-header-area .progress_bar .item_value.cell3 {
  position: absolute;
  right: 140px;
  top: 0;
}
@media (max-width: 767px) {
  .about2-section-area .about-header-area .progress_bar .item_value.cell3 {
    right: 0;
  }
}
.about2-section-area .about-header-area .progress_bar .item_bar {
  position: relative;
  height: 8px;
  width: 100%;
  background-color: var(--ztc-bg-bg-6);
  border-radius: 4px;
  margin-top: 16px;
}
.about2-section-area .about-header-area .progress_bar .item_bar .progress {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  height: 8px;
  margin: 0;
  background-color: var(--ztc-text-text-5);
  border-radius: 4px;
  transition: width 100ms ease;
}
.about2-section-area .about-header-area .header-btn3 {
  margin-top: 12px;
}

.about3-section-area {
  position: relative;
  z-index: 1;
}
.about3-section-area .about3-header-area .misson-text {
  margin-top: 24px;
}
.about3-section-area .about3-header-area .misson-text p {
  font-size: var(--ztc-font-size-font-s24);
  font-family: var(--ztc-family-font1);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-7);
  line-height: var(--ztc-font-size-font-s24);
}
.about3-section-area .about3-header-area .misson-text ul li {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  color: var(--ztc-text-text-8);
  font-weight: var(--ztc-weight-semibold);
  margin-top: 16px;
}
.about3-section-area .about3-header-area .misson-text ul li img {
  margin: 0 8px 0 0;
}
.about3-section-area .about3-header-area .btn-area {
  margin-top: 32px;
}
.about3-section-area .about-images-area {
  position: relative;
}
.about3-section-area .about-images-area .img1 img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
}
.about3-section-area .about-images-area .img1 .about-footer-bottom {
  display: flex;
  align-items: center;
  background: var(--ztc-bg-bg-7);
  border-radius: 4px;
  padding: 32px 24px;
  margin-top: 32px;
}
.about3-section-area .about-images-area .img1 .about-footer-bottom .content {
  margin-left: 16px;
}
.about3-section-area .about-images-area .img1 .about-footer-bottom .content span {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s20);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-1);
  text-transform: capitalize;
  font-weight: var(--ztc-weight-bold);
  display: inline-block;
}

.about4-section-area .about-header-area {
  padding: 0 82px 0 0;
}
@media (max-width: 767px) {
  .about4-section-area .about-header-area {
    margin-bottom: 30px;
    padding: 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about4-section-area .about-header-area {
    margin-bottom: 30px;
    padding: 0;
  }
}
.about4-section-area .about-header-area .counter-box {
  background: var(--ztc-bg-bg-14);
  text-align: center;
  margin-top: 30px;
  padding: 24px;
  border-radius: 4px;
  transition: all 0.4s;
}
.about4-section-area .about-header-area .counter-box:hover {
  background: var(--ztc-bg-bg-11);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.about4-section-area .about-header-area .counter-box:hover h2 {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.about4-section-area .about-header-area .counter-box:hover p {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.about4-section-area .about-header-area .counter-box h2 {
  line-height: 44px;
  transition: all 0.4s;
}
.about4-section-area .about-header-area .counter-box p {
  line-height: 16px;
  transition: all 0.4s;
}
.about4-section-area .about-header-area .btn-area {
  margin-top: 32px;
}
.about4-section-area .about-images-area {
  position: relative;
  z-index: 1;
}
.about4-section-area .about-images-area .img1 {
  position: relative;
}
.about4-section-area .about-images-area .img1 .elements2 {
  position: absolute;
  top: -43px;
  right: -24px;
  height: 100px;
  width: 100px;
  -o-object-fit: contain;
     object-fit: contain;
}
.about4-section-area .about-images-area .img1 img {
  height: 415px;
  width: 100%;
  border-radius: 4px;
  transition: all 0.4s;
  -o-object-fit: cover;
  object-fit: cover;
}
@media (max-width: 767px) {
  .about4-section-area .about-images-area .img1 img {
    width: 100%;
    height: 100%;
  }
}
.about4-section-area .about-images-area .img2 {
  position: absolute;
  right: 76px;
  bottom: 0px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about4-section-area .about-images-area .img2 {
    right: 82px;
    bottom: -10px;
  }
}
@media (max-width: 767px) {
  .about4-section-area .about-images-area .img2 {
    position: relative;
    right: 0;
    left: 0;
  }
}
.about4-section-area .about-images-area .img2 img {
  height: 282px;
  width: 282px;
  border-radius: 4px;
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.4s;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about4-section-area .about-images-area .img2 img {
    width: 310px;
  }
}
@media (max-width: 767px) {
  .about4-section-area .about-images-area .img2 img {
    width: 100%;
    margin-top: 20px;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
  }
}
.about4-section-area .about-images-area .content-experiance {
  background: var(--ztc-bg-bg-11);
  border-radius: 4px;
  display: inline-block;
  padding: 32px 40px;
  transition: all 0.4s;
  text-align: center;
  margin-top: 8px;
  width: 268px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about4-section-area .about-images-area .content-experiance {
    width: 290px;
  }
}
@media (max-width: 767px) {
  .about4-section-area .about-images-area .content-experiance {
    width: 100%;
    margin-top: 30px;
  }
}
.about4-section-area .about-images-area .content-experiance h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-1);
  font-weight: var(--ztc-weight-bold);
  transition: all 0.4s;
  margin-bottom: 16px;
}
.about4-section-area .about-images-area .content-experiance h2 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s44);
  line-height: var(--ztc-font-size-font-s44);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 8px;
}
.about4-section-area .about-images-area .content-experiance p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  transition: all 0.4s;
}

.hero1-section-area.about-bg-area {
  position: relative;
  padding: 250px 0 130px !important;
}
@media (max-width: 767px) {
  .hero1-section-area.about-bg-area {
    padding: 150px 0 100px !important;
  }
}
.hero1-section-area.about-bg-area .header-img1 {
  width: 100% !important;
}
.hero1-section-area.about-bg-area .hero-heading-area h1 {
  margin-bottom: 0;
}
.hero1-section-area.about-bg-area .hero-heading-area .backline {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  display: inline-block;
  opacity: 90%;
  margin-top: 16px;
}
.hero1-section-area.about-bg-area .hero-heading-area .backline span {
  font-weight: var(--ztc-weight-bold);
  line-height: var(--ztc-font-size-font-s24);
}
.hero1-section-area.about-bg-area .hero-heading-area .backline i {
  margin: 0 4px;
}

.aboutpage-inner {
  background: var(--ztc-bg-bg-1) !important;
  position: relative;
}
.aboutpage-inner .about3-header-area .misson-text p {
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
}
.aboutpage-inner .about3-header-area .misson-text ul li {
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-3);
}
.aboutpage-inner .about-images-area .about-footer-bottom {
  background: var(--ztc-text-text-2) !important;
}

.about-innerpage .about-images-area .img1::after {
  background: var(--ztc-text-text-2);
}
.about-innerpage .about-header-area .progress_bar_item .item_label {
  color: var(--ztc-text-text-3);
  font-weight: var(--ztc-weight-bold);
}
.about-innerpage .about-header-area .progress_bar_item .item-value {
  color: var(--ztc-text-text-3);
}
.about-innerpage .about-header-area .progress_bar_item .item-bar .progress {
  background: var(--ztc-text-text-3);
}
.about-innerpage .about-header-area .btn-area {
  margin-top: 32px;
}

/*============= ABOUT CSS AREA ENDS ===============*/
/*============= SERVICE CSS AREA ===============*/
.service1-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-bg-bg-1);
}
.service1-section-area .service-header-area {
  margin-bottom: 60px;
}
.service1-section-area .service-auhtor-boxarea {
  position: relative;
  z-index: 1;
  transition: all 0.4s;
  background: var(--ztc-text-text-1);
  border-radius: 4px;
  margin-bottom: 30px;
}
.service1-section-area .service-auhtor-boxarea:hover .img1::after {
  height: 100%;
  transition: all 0.4s;
}
.service1-section-area .service-auhtor-boxarea:hover .img1 img {
  transform: scale(1.1) rotate(-4deg);
  transition: all 0.4s;
}
.service1-section-area .service-auhtor-boxarea:hover .content-area::after {
  height: 100%;
  transition: all 0.4s;
}
.service1-section-area .service-auhtor-boxarea:hover .content-area a {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.service1-section-area .service-auhtor-boxarea:hover .content-area p {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  opacity: 0.8;
}
.service1-section-area .service-auhtor-boxarea:hover .content-area .readmore {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.service1-section-area .service-auhtor-boxarea:hover .content-area h3 {
  background: var(--ztc-text-text-1);
  transition: all 0.4s;
  color: var(--ztc-text-text-3);
}
.service1-section-area .service-auhtor-boxarea .img1 {
  height: 100%;
  width: 100%;
  transition: all 0.4s ease-in-out;
  overflow: hidden;
  border-radius: 4px 4px 0 0;
  position: relative;
  z-index: 1;
}
.service1-section-area .service-auhtor-boxarea .img1::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  transition: all 0.4s;
  background: var(--ztc-text-text-2);
  opacity: 0.5;
  left: 0;
  top: 0;
}
.service1-section-area .service-auhtor-boxarea .img1 img {
  height: 100%;
  width: 100%;
  border-radius: 4px 4px 0 0;
  transition: all 0.4s;
}
.service1-section-area .service-auhtor-boxarea .content-area {
  padding: 64px 38px 32px 38px;
  text-align: center;
  position: relative;
  transition: all 0.4s;
  z-index: 1;
}
@media (max-width: 767px) {
  .service1-section-area .service-auhtor-boxarea .content-area {
    padding: 64px 32px 32px 32px;
  }
}
.service1-section-area .service-auhtor-boxarea .content-area::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  left: 0;
  bottom: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-2);
  z-index: -1;
  border-radius: 0 0 4px 4px;
}
.service1-section-area .service-auhtor-boxarea .content-area h3 {
  font-size: var(--ztc-font-size-font-s24);
  font-family: var(--ztc-family-font1);
  color: var(--ztc-text-text-1);
  font-weight: var(--ztc-weight-bold);
  height: 80px;
  width: 80px;
  text-align: center;
  line-height: 80px;
  background: var(--ztc-text-text-3);
  border-radius: 50%;
  transition: all 0.4s;
  margin: 0 auto;
  margin-top: -100px;
  margin-bottom: 24px;
  position: relative;
  z-index: 2;
}
.service1-section-area .service-auhtor-boxarea .content-area a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  display: inline-block;
  color: var(--ztc-text-text-3);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 16px;
  transition: all 0.4s;
}
.service1-section-area .service-auhtor-boxarea .content-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s26);
  color: var(--ztc-text-text-4);
  font-weight: var(--ztc-weight-medium);
  transition: all 0.4s;
}
.service1-section-area .service-auhtor-boxarea .content-area a.readmore {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-3);
  display: inline-block;
  margin-bottom: 0;
  margin-top: 24px;
  font-weight: var(--ztc-weight-bold);
  transition: all 0.4s;
}
.service1-section-area .service-auhtor-boxarea .content-area a.readmore i {
  margin-left: 4px;
  transform: rotate(-45deg);
}

.service2-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-bg-bg-1);
}
.service2-section-area .service-header-area {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .service2-section-area .service-header-area {
    margin-bottom: 30px;
  }
}
.service2-section-area .service2-author-boxarea {
  background: var(--ztc-text-text-1);
  position: relative;
  border-radius: 4px;
  transition: all 0.4s;
  margin-bottom: 30px;
}
.service2-section-area .service2-author-boxarea:hover .images::after {
  height: 100%;
  width: 100%;
  transition: all 0.4s;
}
.service2-section-area .service2-author-boxarea:hover .images img {
  transform: scale(1.1) rotate(4deg);
  transition: all 0.4s;
}
.service2-section-area .service2-author-boxarea:hover .icons {
  transition: all 0.4s;
  transform: rotateY(-180deg);
}
.service2-section-area .service2-author-boxarea .images {
  overflow: hidden;
  border-radius: 4px 4px 0 0;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.service2-section-area .service2-author-boxarea .images::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  left: 0;
  top: 0;
  background: var(--ztc-bg-bg-3);
  opacity: 0.7;
  transition: all 0.4s;
}
.service2-section-area .service2-author-boxarea .images img {
  height: 100%;
  width: 100%;
  border-radius: 4px 4px 0 0;
  transition: all 0.4s;
}
.service2-section-area .service2-author-boxarea .icons {
  height: 70px;
  width: 70px;
  text-align: center;
  line-height: 70px;
  border-radius: 4px;
  background: var(--ztc-bg-bg-3);
  transition: all 0.4s;
  position: absolute;
  top: 49%;
  right: 16px;
  bottom: 16px;
  z-index: 2;
}
.service2-section-area .service2-author-boxarea .service-content {
  padding: 24px;
}
.service2-section-area .service2-author-boxarea .service-content a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  display: inline-block;
  transition: all 0.4s;
  margin-bottom: 16px;
}
.service2-section-area .service2-author-boxarea .service-content a:hover {
  color: var(--ztc-bg-bg-3);
  transition: all 0.4s;
}
.service2-section-area .service2-author-boxarea .service-content p {
  font-size: var(--ztc-font-size-font-s18);
  font-family: var(--ztc-family-font1);
  line-height: var(--ztc-font-size-font-s26);
  color: var(--ztc-text-text-6);
  font-weight: var(--ztc-weight-medium);
}
.service2-section-area .service2-author-boxarea .service-content .readmore {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  margin-bottom: 0;
  display: inline-block;
  transition: all 0.4s;
  font-weight: var(--ztc-weight-bold);
  margin-top: 24px;
}
.service2-section-area .service2-author-boxarea .service-content .readmore:hover {
  color: var(--ztc-bg-bg-3);
  transition: all 0.4s;
}
.service2-section-area .service2-author-boxarea .service-content .readmore i {
  margin-left: 4px;
  transform: rotate(-45deg);
}

.service3-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-text-text-7);
}
.service3-section-area .service-header-area {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .service3-section-area .service-header-area {
    margin-bottom: 30px;
  }
}
.service3-section-area .service-auhtor-boxarea {
  text-align: center;
  position: relative;
  z-index: 1;
  background: #221D19;
  border-radius: 4px;
  padding: 32px;
  transition: all 0.4s;
  margin-bottom: 30px;
}
.service3-section-area .service-auhtor-boxarea:hover {
  background: var(--ztc-bg-bg-7);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.service3-section-area .service-auhtor-boxarea:hover .icons {
  background: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: rotateY(-180deg);
}
.service3-section-area .service-auhtor-boxarea:hover .icons img {
  filter: none;
  transition: all 0.4s;
}
.service3-section-area .service-auhtor-boxarea .icons {
  height: 70px;
  width: 70px;
  text-align: center;
  line-height: 70px;
  display: inline-block;
  border-radius: 4px;
  background: var(--ztc-bg-bg-7);
  transition: all 0.4s;
  margin: 0 auto;
}
.service3-section-area .service-auhtor-boxarea .icons img {
  filter: brightness(0) invert(1);
  transition: all 0.4s;
}
.service3-section-area .service-auhtor-boxarea .content-area {
  margin-top: 32px;
}
.service3-section-area .service-auhtor-boxarea .content-area a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  display: inline-block;
  font-weight: var(--ztc-weight-bold);
}
.service3-section-area .service-auhtor-boxarea .content-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s26);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  transition: all 0.4s;
  padding-top: 16px;
  margin-bottom: 32px;
}
.service3-section-area .service-auhtor-boxarea .content-area a.readmore {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  display: inline-block;
  font-weight: var(--ztc-weight-bold);
}
.service3-section-area .service-auhtor-boxarea .content-area a.readmore i {
  margin-left: 4px;
  transform: rotate(-45deg);
}

.service1-section-area .pagination-area ul {
  text-align: center;
  justify-content: center;
  margin-top: 30px;
}
.service1-section-area .pagination-area ul li a {
  height: 50px;
  width: 50px;
  display: inline-block;
  border-radius: 4px !important;
  transition: all 0.4s;
  border: none;
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s40);
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-3);
  margin: 0 16px;
  box-shadow: none;
  background: var(--ztc-bg-bg-1);
}
.service1-section-area .pagination-area ul li a:hover {
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
}
.service1-section-area .pagination-area ul li .page-link.active {
  background: var(--ztc-text-text-2) !important;
  color: var(--ztc-text-text-1);
}

.leftside {
  background: var(--ztc-text-text-1) !important;
}
.leftside .service-auhtor-boxarea {
  background: var(--ztc-bg-bg-1);
}

.service-leftside-area {
  position: relative;
}
.service-leftside-area .service-leftside .service-author-list {
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  transition: all 0.4s;
  padding: 32px 20px;
}
.service-leftside-area .service-leftside .service-author-list h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  margin-bottom: 4px;
}
.service-leftside-area .service-leftside .service-author-list ul li a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  font-family: var(--ztc-family-font1);
  padding: 20px 24px;
  background: var(--ztc-text-text-1);
  margin-top: 20px;
  border-radius: 4px;
  height: 60px;
  transition: all 0.4s;
}
.service-leftside-area .service-leftside .service-author-list ul li a i {
  transition: all 0.4s;
}
.service-leftside-area .service-leftside .service-author-list ul li a:hover {
  background: var(--ztc-text-text-2);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.service-leftside-area .service-leftside .service-author-list ul li a:hover i {
  transform: rotate(90deg);
  transition: all 0.4s;
}
.service-leftside-area .service-leftside .helping-area {
  background: var(--ztc-text-text-2);
  border-radius: 4px;
  margin-top: 32px;
  padding: 32px 24px;
}
.service-leftside-area .service-leftside .helping-area h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s30);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 4px;
  color: var(--ztc-text-text-1);
}
.service-leftside-area .service-leftside .helping-area .btn-area {
  margin-top: 24px;
}
.service-leftside-area .service-leftside .helping-area .btn-area .header-btn1 {
  background: var(--ztc-text-text-1);
  color: var(--ztc-text-text-3);
}
.service-leftside-area .service-leftside .helping-area .btn-area .header-btn1 i {
  margin: 0 6px 0 0;
}
.service-leftside-area .service-leftside .download-broucher {
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  margin-top: 32px;
  padding: 32px 24px;
}
.service-leftside-area .service-leftside .download-broucher h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s30);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 4px;
  color: var(--ztc-text-text-3);
}
.service-leftside-area .service-leftside .download-broucher p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
  margin-top: 16px;
}
.service-leftside-area .service-leftside .download-broucher .btn-area .header-btn1 {
  margin-top: 24px;
}
.service-leftside-area .service-leftside .download-broucher .btn-area .header-btn1 img {
  filter: brightness(0) invert(1);
  transition: all 0.4s;
  margin: -5px 4px 0 0;
}
.service-leftside-area .service-leftside .download-broucher .btn-area .header-btn1:hover {
  background: var(--ztc-text-text-3);
  transition: all 0.4s;
  transform: translateY(-5px);
  color: var(--ztc-text-text-1);
}
.service-leftside-area .service-leftside .download-broucher .btn-area .header-btn2 {
  background: var(--ztc-text-text-1);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  margin-left: 16px;
  margin-top: 24px;
  padding: 16px 20px;
}
@media (max-width: 767px) {
  .service-leftside-area .service-leftside .download-broucher .btn-area .header-btn2 {
    margin-left: 0;
  }
}
.service-leftside-area .service-leftside .download-broucher .btn-area .header-btn2 img {
  transition: all 0.4s;
  margin: -5px 4px 0 0;
}
.service-leftside-area .service-leftside .download-broucher .btn-area .header-btn2:hover {
  background: var(--ztc-text-text-3);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.service-leftside-area .service-leftside .download-broucher .btn-area .header-btn2:hover img {
  transition: all 0.4s;
  filter: brightness(0) invert(1);
}
.service-leftside-area .service-leftside .social-icons {
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  margin-top: 32px;
  padding: 32px 24px;
}
.service-leftside-area .service-leftside .social-icons h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s30);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 4px;
  color: var(--ztc-text-text-3);
}
.service-leftside-area .service-leftside .social-icons ul {
  margin-top: 24px;
}
.service-leftside-area .service-leftside .social-icons ul li {
  display: inline-block;
}
.service-leftside-area .service-leftside .social-icons ul li a {
  display: inline-block;
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  background: var(--ztc-text-text-1);
  transition: all 0.4s;
  color: var(--ztc-text-text-3);
  margin: 0 8px 0 0;
}
.service-leftside-area .service-leftside .social-icons ul li a:hover {
  background: var(--ztc-text-text-2);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.service-leftside-area .service-leftside .tags-area {
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  margin-top: 32px;
  padding: 32px 24px;
}
.service-leftside-area .service-leftside .tags-area h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s30);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 4px;
  color: var(--ztc-text-text-3);
}
.service-leftside-area .service-leftside .tags-area ul li {
  display: inline-block;
}
.service-leftside-area .service-leftside .tags-area ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  display: inline-block;
  background: var(--ztc-text-text-1);
  border-radius: 4px;
  padding: 8px 12px;
  margin: 12px 12px 0 0;
}
.service-leftside-area .service-leftside .tags-area ul li a:hover {
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  transform: translateY(-3px);
  color: var(--ztc-text-text-1);
}

.service-rightside-area {
  padding: 0 0 0 50px;
}
@media (max-width: 767px) {
  .service-rightside-area {
    padding: 0;
    margin-top: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .service-rightside-area {
    padding: 0;
    margin-top: 30px;
  }
}
.service-rightside-area .img1 img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
}
.service-rightside-area .img1 ul li {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-3);
}
.service-rightside-area .img1 ul li img {
  height: 20px;
  width: 20px;
  margin: 0 8px 0 0;
}
.service-rightside-area p {
  margin-bottom: 16px !important;
}
.service-rightside-area h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s32);
  line-height: var(--ztc-font-size-font-s40);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-weight-bold);
  margin-bottom: 16px;
  text-transform: capitalize;
}
@media (max-width: 767px) {
  .service-rightside-area .faq-auhtor-area1 {
    margin-top: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .service-rightside-area .faq-auhtor-area1 {
    margin-top: 30px;
  }
}
.service-rightside-area .faq-auhtor-area1 .accordion .accordion-item {
  border: none;
  border-radius: 6px;
  background: var(--ztc-bg-bg-1);
}
.service-rightside-area .faq-auhtor-area1 .accordion .accordion-item .accordion-button::after {
  filter: brightness(0);
  position: absolute;
  right: 16px;
}
.service-rightside-area .faq-auhtor-area1 .accordion .accordion-item h2 {
  margin-bottom: 0 !important;
}
.service-rightside-area .faq-auhtor-area1 .accordion .accordion-item .accordion-header .accordion-button:not(.collapsed) {
  background: var(--ztc-bg-bg-1);
}
.service-rightside-area .faq-auhtor-area1 .accordion .accordion-item .accordion-header button {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  color: var(--ztc-text-text-5);
  display: inline-block;
  font-weight: var(--ztc-weight-bold);
  text-transform: capitalize;
  border: none;
  box-shadow: none;
  padding: 20px 16px 20px 16px;
  background: var(--ztc-bg-bg-1);
}
@media (max-width: 767px) {
  .service-rightside-area .faq-auhtor-area1 .accordion .accordion-item .accordion-header button {
    line-height: var(--ztc-font-size-font-s26);
  }
}
.service-rightside-area .faq-auhtor-area1 .accordion .accordion-item .accordion-body {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-6);
  opacity: 80%;
  line-height: var(--ztc-font-size-font-s26);
  padding: 0 16px 16px 16px;
}

.rightsidebar {
  padding: 0 50px 0 0 !important;
}
@media (max-width: 767px) {
  .rightsidebar {
    padding: 0 !important;
    margin-bottom: 30px;
    margin-top: 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rightsidebar {
    padding: 0 !important;
    margin-bottom: 30px;
    margin-top: 0;
  }
}

/*============= SERVICE CSS AREA ENDS===============*/
/*============= HERO CSS AREA ===============*/
.slider-header-carousel .owl-item.active .hero1-section-area .header-img1 {
  transform: scale(1.2);
}
.slider-header-carousel .owl-item.active .hero1-section-area .hero-heading-area h5 {
  transition: transform 1400ms ease, opacity 1400ms ease;
  transform: translateX(0px);
  opacity: 1;
}
.slider-header-carousel .owl-item.active .hero1-section-area .hero-heading-area h1.main-heading {
  transition: transform 1600ms ease, opacity 1600ms ease;
  transform: translateX(0px);
  opacity: 1;
}
.slider-header-carousel .owl-item.active .hero1-section-area .hero-heading-area p.pera {
  transition: transform 1700ms ease, opacity 1700ms ease;
  transform: translateX(0px);
  opacity: 1 !important;
}
.slider-header-carousel .owl-item.active .hero1-section-area .hero-heading-area .btn-area {
  transition: transform 1800ms ease, opacity 1800ms ease;
  transform: translateX(0px);
  opacity: 1;
}
.slider-header-carousel .owl-item.active .hero1-section-area .hero-heading-area .header-bottom-images {
  transition: transform 2000ms ease, opacity 2000ms ease;
  transform: translateX(0px);
  opacity: 1;
}
.slider-header-carousel .owl-nav {
  position: absolute;
  right: 50px;
  top: 45%;
}
@media (max-width: 767px) {
  .slider-header-carousel .owl-nav {
    top: 80%;
    display: none;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .slider-header-carousel .owl-nav {
    top: 40%;
    right: 30px;
  }
}
.slider-header-carousel .owl-nav button {
  height: 60px;
  width: 60px;
  text-align: center;
  border: none;
  outline: none;
  line-height: 60px;
  border-radius: 4px;
  transition: all 0.4s;
  font-size: 20px;
  color: var(--ztc-text-text-1);
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(30px);
          backdrop-filter: blur(30px);
  border: 1px solid var(--ztc-text-text-1);
}
.slider-header-carousel .owl-nav button:hover {
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
}
.slider-header-carousel .owl-nav button i {
  color: var(--ztc-text-text-1);
  font-size: var(--ztc-font-size-font-s20);
}
.slider-header-carousel .owl-nav .owl-prev {
  position: absolute;
  top: -70px;
}
.slider-header-carousel .owl-dots {
  position: absolute;
  bottom: 36px;
  margin: 0 auto;
  text-align: center;
  left: 50%;
}
.slider-header-carousel .owl-dots button {
  height: 12px;
  width: 12px;
  display: inline-block;
  background: #767478;
  transition: all 0.4s;
  border-radius: 50%;
  margin: 0 18px 0 0;
  position: relative;
  z-index: 1;
}
.slider-header-carousel .owl-dots button::after {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  border: 1px solid #fff;
  top: -4px;
  left: -4px;
  border-radius: 50%;
  visibility: hidden;
  opacity: 1;
}
.slider-header-carousel .owl-dots button.active {
  background: var(--ztc-text-text-1);
}
.slider-header-carousel .owl-dots button.active::after {
  visibility: visible;
  opacity: 1;
}

.hero1-section-area {
  position: relative;
  z-index: 1;
  padding: 250px 0 130px;
}
@media (max-width: 767px) {
  .hero1-section-area {
    padding: 200px 0 130px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero1-section-area {
    padding: 200px 0 130px;
  }
}
.hero1-section-area .header-img1 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  width: 100% !important;
  -o-object-fit: cover;
     object-fit: cover;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  transform: scale(1);
  transition: transform 8000ms ease, opacity 1800ms ease-in;
  z-index: -1;
}
.hero1-section-area::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  background: var(--ztc-text-text-3);
  opacity: 0.6;
  z-index: -1;
}
.hero1-section-area .hero-heading-area h5 {
  transition: transform 1300ms ease, opacity 1500ms ease;
  transform: translateX(-300px);
  opacity: 0;
  position: relative;
}
.hero1-section-area .hero-heading-area h1.main-heading {
  transition: transform 1400ms ease, opacity 1400ms ease;
  transform: translateX(-300px);
  position: relative;
  opacity: 0;
}
.hero1-section-area .hero-heading-area p.pera {
  transition: transform 1400ms ease, opacity 1400ms ease;
  transform: translateX(-300px);
  position: relative;
  opacity: 0 !important;
}
.hero1-section-area .hero-heading-area .btn-area {
  transition: transform 1400ms ease, opacity 1400ms ease;
  transform: translateX(-300px);
  position: relative;
  opacity: 0;
}
.hero1-section-area .hero-heading-area .btn-area .header-btn1 {
  margin-top: 30px;
}
.hero1-section-area .hero-heading-area .btn-area .header-btn2 {
  margin-left: 20px;
  margin-top: 30px;
}
@media (max-width: 767px) {
  .hero1-section-area .hero-heading-area .btn-area .header-btn2 {
    margin-left: 0;
  }
}
.hero1-section-area .hero-heading-area .header-bottom-images {
  display: flex;
  align-items: center;
  margin-top: 32px;
  transition: transform 1500ms ease, opacity 1500ms ease;
  transform: translateX(-300px);
  position: relative;
  opacity: 0;
}
.hero1-section-area .hero-heading-area .header-bottom-images .content {
  margin-left: 20px;
}
.hero1-section-area .hero-heading-area .header-bottom-images .content ul li {
  display: inline-block;
  color: #FFD600;
}
.hero1-section-area .hero-heading-area .header-bottom-images .content ul li:nth-child(5) {
  color: var(--ztc-text-text-1);
}
.hero1-section-area .hero-heading-area .header-bottom-images .content p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  margin-top: 12px;
}
.hero1-section-area .hero-heading-area .header-bottom-images .content p span {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-1);
  display: inline-block;
  margin: 0 6px 0 0;
}
.hero1-section-area .circle-arrow {
  position: absolute;
  right: 135px;
  bottom: 35px;
}
@media (max-width: 767px) {
  .hero1-section-area .circle-arrow {
    position: relative;
    right: 0;
    left: 0;
    bottom: -30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero1-section-area .circle-arrow {
    right: 50px;
  }
}
.hero1-section-area .circle-arrow a {
  height: 200px;
  width: 200px;
  text-align: center;
  line-height: 200px;
  display: inline-block;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}
.hero1-section-area .circle-arrow a .circle1 {
  height: 154px;
  width: 154px;
  text-align: center;
  line-height: 154px;
  position: relative;
  margin-left: 22px;
  margin-top: 22px;
}
.hero1-section-area .circle-arrow a .arrow1 {
  position: absolute;
  top: 80px;
  left: 80px;
  height: 40px;
  width: 40px;
}

.hero2-section-area {
  position: relative;
  z-index: 1;
  padding: 160px 0 30px;
}
@media (max-width: 767px) {
  .hero2-section-area .hero-header {
    margin-bottom: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero2-section-area .hero-header {
    margin-bottom: 30px;
  }
}
.hero2-section-area .hero-header .btn-area .header-btn3 {
  margin-top: 32px;
}
.hero2-section-area .hero-header .btn-area .header-btn3:hover {
  color: var(--ztc-text-text-5);
  transition: all 0.4s;
}
.hero2-section-area .hero-header .btn-area .header-btn3::after {
  background: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.hero2-section-area .hero-header .btn-area .header-btn4 {
  margin-top: 32px;
  margin-left: 20px;
}
@media (max-width: 767px) {
  .hero2-section-area .hero-header .btn-area .header-btn4 {
    margin-left: 0;
  }
}
.hero2-section-area .hero-header .counter-header-area {
  display: flex;
  align-items: center;
  margin-top: 60px;
}
@media (max-width: 767px) {
  .hero2-section-area .hero-header .counter-header-area {
    display: block;
    margin-top: 30px;
    text-align: center;
    justify-content: center;
  }
}
.hero2-section-area .hero-header .counter-header-area .counter-area {
  margin: 0 60px 0 0;
}
@media (max-width: 767px) {
  .hero2-section-area .hero-header .counter-header-area .counter-area {
    margin: 0 0 20px 0;
    text-align: center;
  }
}
.hero2-section-area .hero-header .counter-header-area .counter-area h2 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s32);
  line-height: var(--ztc-font-size-font-s32);
  color: var(--ztc-text-text-1);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 16px;
}
.hero2-section-area .hero2-images-area {
  padding: 0 0 0 50px;
}
@media (max-width: 767px) {
  .hero2-section-area .hero2-images-area {
    padding: 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero2-section-area .hero2-images-area {
    padding: 0;
  }
}
.hero2-section-area .hero2-images-area img.circle2.keyframe5 {
  position: absolute;
  margin-top: -60px;
  right: 250px;
  z-index: -1;
}
@media (max-width: 767px) {
  .hero2-section-area .hero2-images-area img.circle2.keyframe5 {
    display: none;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero2-section-area .hero2-images-area img.circle2.keyframe5 {
    right: 0px;
  }
}
.hero2-section-area .hero2-images-area .images {
  margin-bottom: 30px;
  position: relative;
  z-index: 1;
}
.hero2-section-area .hero2-images-area .images .circle2 {
  height: 140px;
  width: 140px;
  -o-object-fit: cover;
     object-fit: cover;
  position: absolute;
  right: -40px;
  top: -40px;
  z-index: -1;
}
.hero2-section-area .hero2-images-area .images img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
}

.hero3-section-area {
  background: var(--ztc-bg-bg-1);
  position: relative;
  z-index: 1;
  padding: 160px 0 0 0;
  overflow: hidden;
}
.hero3-section-area .tower1 {
  position: absolute;
  bottom: 50px;
  left: 0;
}
.hero3-section-area .hero-header-area .header-btn5 {
  margin-top: 20px;
}
.hero3-section-area .hero-header-area .header-btn6 {
  margin-top: 20px;
  margin-left: 20px;
}
@media (max-width: 767px) {
  .hero3-section-area .hero-header-area .header-btn6 {
    margin-left: 0;
  }
}
.hero3-section-area .header-images-area {
  position: relative;
}
.hero3-section-area .header-images-area .header-img {
  position: relative;
  z-index: 1;
}
.hero3-section-area .header-images-area .header-img::after {
  position: absolute;
  content: "";
  height: 420px;
  width: 420px;
  border-radius: 50%;
  background: var(--ztc-bg-bg-7);
  left: 18%;
  top: 20%;
  z-index: -1;
  animation-name: animation-5;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}
@media (max-width: 767px) {
  .hero3-section-area .header-images-area .header-img::after {
    display: none;
  }
}
.hero3-section-area .header-images-area .header-img img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
}
.hero3-section-area .header-images-area .shapes .arrow1 {
  position: absolute;
  top: 18%;
  left: -100px;
}
.hero3-section-area .header-images-area .shapes .lite1 {
  position: absolute;
  top: 35px;
  left: 14%;
}

.hero4-section-area {
  position: relative;
  z-index: 1;
  padding: 200px 0 100px;
  background: var(--ztc-bg-bg-13);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero4-section-area .hero4-header-textarea {
    text-align: center;
  }
}
@media (max-width: 767px) {
  .hero4-section-area .hero4-header-textarea {
    text-align: center;
  }
}
.hero4-section-area .hero4-header-textarea .header-text {
  margin-bottom: 32px;
}
.hero4-section-area .hero4-header-textarea .header-text .btn-area .header-btn7 {
  margin-top: 20px;
}
.hero4-section-area .hero4-header-textarea .header-text .btn-area .header-btn8 {
  margin-top: 20px;
  margin-left: 20px;
}
@media (max-width: 767px) {
  .hero4-section-area .hero4-header-textarea .header-text .btn-area .header-btn8 {
    margin-left: 0;
  }
}
.hero4-section-area .hero4-header-textarea .header-left-side {
  position: relative;
}
.hero4-section-area .hero4-header-textarea .header-left-side img.bottom2 {
  margin-bottom: 24px;
}
.hero4-section-area .hero4-header-textarea .header-left-side .arrow2 {
  position: relative;
  left: 22%;
  right: 50%;
  padding: 60px 0 60px 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero4-section-area .hero4-header-textarea .header-left-side .arrow2 {
    left: 0;
  }
}
@media (max-width: 767px) {
  .hero4-section-area .hero4-header-textarea .header-left-side .arrow2 {
    left: 0;
  }
}
.hero4-section-area .hero4-header-textarea .header-left-side p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-10);
  line-height: var(--ztc-font-size-font-s16);
}
.hero4-section-area .hero4-header-textarea .header-left-side p span {
  font-size: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  margin: 0 8px 0 0;
}
.hero4-section-area .hero4-header-textarea .header-left-side ul {
  margin-top: 10px;
}
.hero4-section-area .hero4-header-textarea .header-left-side ul li {
  display: inline-block;
}
.hero4-section-area .hero4-header-textarea .header-left-side .percent-electri h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s44);
  line-height: var(--ztc-font-size-font-s44);
  color: var(--ztc-text-text-10);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 8px;
}
.hero4-section-area .hero4-header-textarea .header-left-side .percent-electri p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-11);
}
.hero4-section-area .hero4-header-textarea .header-images {
  text-align: center;
  position: relative;
}
.hero4-section-area .hero4-header-textarea .header-images .elements1 {
  position: absolute;
  top: 30px;
  right: 60px;
  width: 123px;
  height: 130px;
  -o-object-fit: contain;
     object-fit: contain;
}
.hero4-section-area .hero4-header-textarea .header-images img.header-img6 {
  transition: all 0.4s;
  width: 500px;
  height: 600px;
  border-radius: 4px;
  -o-object-fit: contain;
     object-fit: contain;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero4-section-area .hero4-header-textarea .header-images img.header-img6 {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
    border-radius: 4px;
  }
}
@media (max-width: 767px) {
  .hero4-section-area .hero4-header-textarea .header-images img.header-img6 {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
    border-radius: 4px;
  }
}
.hero4-section-area .hero4-header-textarea .header-images .arrow2 {
  text-align: start;
  position: absolute;
  bottom: 10%;
  left: -60px;
  transform: rotate(-90deg);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero4-section-area .hero4-header-textarea .header-images .arrow2 {
    display: none;
  }
}
@media (max-width: 767px) {
  .hero4-section-area .hero4-header-textarea .header-images .arrow2 {
    display: none;
  }
}
.hero4-section-area .hero4-header-textarea .images-text-area {
  position: relative;
  left: -13%;
}
@media (max-width: 767px) {
  .hero4-section-area .hero4-header-textarea .images-text-area {
    left: -35%;
  }
}
.hero4-section-area .hero4-header-textarea .images-text-area .content-area {
  display: flex;
  align-items: center;
  position: relative;
  left: 30%;
  top: 30px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero4-section-area .hero4-header-textarea .images-text-area .content-area {
    left: 57%;
  }
}
@media (max-width: 767px) {
  .hero4-section-area .hero4-header-textarea .images-text-area .content-area {
    left: 60%;
  }
}
.hero4-section-area .hero4-header-textarea .images-text-area .content-area .icons {
  height: 50px;
  width: 50px;
  text-align: center;
  line-height: 50px;
  display: inline-block;
  border-radius: 50%;
  transition: all 0.4s;
  background: var(--ztc-text-text-1);
  margin: 0 16px 0 0;
}
.hero4-section-area .hero4-header-textarea .images-text-area .content-area .pera p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-10);
  line-height: var(--ztc-font-size-font-s26);
}
.hero4-section-area .hero4-header-textarea .images-text-area .pera2 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s26);
  color: var(--ztc-text-text-10);
  opacity: 80%;
  font-weight: var(--ztc-weight-medium);
  margin-top: 16px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero4-section-area .hero4-header-textarea .images-text-area .pera2 p {
    padding-left: 280px;
  }
}
@media (max-width: 767px) {
  .hero4-section-area .hero4-header-textarea .images-text-area .pera2 p {
    padding-left: 150px;
  }
}

/*============= HERO CSS AREA ===============*/
/*============= MOBILE MENU CSS AREA ===============*/
.mobile-header.mobile-haeder1 {
  background: var(--ztc-text-text-3);
  position: fixed;
  width: 100%;
}
.mobile-header.mobile-haeder1 .dots-menu {
  color: var(--ztc-text-text-1);
}

.mobile-sidebar.mobile-sidebar1 {
  background: var(--ztc-text-text-3);
}
.mobile-sidebar.mobile-sidebar1 .menu-close {
  color: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar1 .mobile-nav.mobile-nav1 .header-btn1 {
  width: 100%;
  text-align: center;
  margin-top: 24px;
}
.mobile-sidebar.mobile-sidebar1 .mobile-nav.mobile-nav1 span.submenu-button:before {
  background: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar1 .mobile-nav.mobile-nav1 span.submenu-button::after {
  background: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar1 .mobile-nav.mobile-nav1 ul li a {
  color: var(--ztc-text-text-1);
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
}
.mobile-sidebar.mobile-sidebar1 .mobile-nav.mobile-nav1 .allmobilesection h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-1);
  text-transform: capitalize;
  font-weight: var(--ztc-weight-bold);
  margin-top: 24px;
}
.mobile-sidebar.mobile-sidebar1 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single {
  display: flex;
  margin-top: 16px;
}
.mobile-sidebar.mobile-sidebar1 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single .contact-info-icon {
  margin: 0 8px 0 0;
}
.mobile-sidebar.mobile-sidebar1 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single .contact-info-icon i {
  color: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar1 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single .contact-info-text a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  display: inline-block;
}
.mobile-sidebar.mobile-sidebar1 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul {
  margin-top: 20px;
}
.mobile-sidebar.mobile-sidebar1 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul li {
  display: inline-block;
}
.mobile-sidebar.mobile-sidebar1 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul li a {
  height: 40px;
  width: 40px;
  text-align: center;
  background: var(--ztc-text-text-1);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  display: inline-block;
  border-radius: 50%;
  margin: 0 6px 0 0;
}
.mobile-sidebar.mobile-sidebar1 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul li a:hover {
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
}

.mobile-header.mobile-haeder2 {
  background: var(--ztc-text-text-5);
  position: fixed;
  width: 100%;
}
.mobile-header.mobile-haeder2 .dots-menu {
  color: var(--ztc-text-text-1);
}

.mobile-sidebar.mobile-sidebar2 {
  background: var(--ztc-text-text-5);
}
.mobile-sidebar.mobile-sidebar2 .menu-close {
  color: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar2 .mobile-nav.mobile-nav1 .header-btn3 {
  width: 100%;
  text-align: center;
  margin-top: 24px;
}
.mobile-sidebar.mobile-sidebar2 .mobile-nav.mobile-nav1 span.submenu-button:before {
  background: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar2 .mobile-nav.mobile-nav1 span.submenu-button::after {
  background: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar2 .mobile-nav.mobile-nav1 ul li a {
  color: var(--ztc-text-text-1);
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
}
.mobile-sidebar.mobile-sidebar2 .mobile-nav.mobile-nav1 .allmobilesection h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-1);
  text-transform: capitalize;
  font-weight: var(--ztc-weight-bold);
  margin-top: 24px;
}
.mobile-sidebar.mobile-sidebar2 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single {
  display: flex;
  margin-top: 16px;
}
.mobile-sidebar.mobile-sidebar2 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single .contact-info-icon {
  margin: 0 8px 0 0;
}
.mobile-sidebar.mobile-sidebar2 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single .contact-info-icon i {
  color: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar2 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single .contact-info-text a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  display: inline-block;
}
.mobile-sidebar.mobile-sidebar2 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul {
  margin-top: 20px;
}
.mobile-sidebar.mobile-sidebar2 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul li {
  display: inline-block;
}
.mobile-sidebar.mobile-sidebar2 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul li a {
  height: 40px;
  width: 40px;
  text-align: center;
  background: var(--ztc-text-text-1);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  display: inline-block;
  border-radius: 50%;
  margin: 0 6px 0 0;
}
.mobile-sidebar.mobile-sidebar2 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul li a:hover {
  background: var(--ztc-bg-bg-3);
  transition: all 0.4s;
  color: var(--ztc-text-text-5);
}

.mobile-header.mobile-haeder3 {
  background: var(--ztc-text-text-7);
  position: fixed;
  width: 100%;
}
.mobile-header.mobile-haeder3 .dots-menu {
  color: var(--ztc-text-text-1);
}

.mobile-sidebar.mobile-sidebar3 {
  background: var(--ztc-text-text-7);
}
.mobile-sidebar.mobile-sidebar3 .menu-close {
  color: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar3 .mobile-nav.mobile-nav1 .header-btn5 {
  width: 100%;
  text-align: center;
  margin-top: 24px;
}
.mobile-sidebar.mobile-sidebar3 .mobile-nav.mobile-nav1 span.submenu-button:before {
  background: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar3 .mobile-nav.mobile-nav1 span.submenu-button::after {
  background: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar3 .mobile-nav.mobile-nav1 ul li a {
  color: var(--ztc-text-text-1);
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
}
.mobile-sidebar.mobile-sidebar3 .mobile-nav.mobile-nav1 .allmobilesection h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-1);
  text-transform: capitalize;
  font-weight: var(--ztc-weight-bold);
  margin-top: 24px;
}
.mobile-sidebar.mobile-sidebar3 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single {
  display: flex;
  margin-top: 16px;
}
.mobile-sidebar.mobile-sidebar3 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single .contact-info-icon {
  margin: 0 8px 0 0;
}
.mobile-sidebar.mobile-sidebar3 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single .contact-info-icon i {
  color: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar3 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single .contact-info-text a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  display: inline-block;
}
.mobile-sidebar.mobile-sidebar3 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul {
  margin-top: 20px;
}
.mobile-sidebar.mobile-sidebar3 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul li {
  display: inline-block;
}
.mobile-sidebar.mobile-sidebar3 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul li a {
  height: 40px;
  width: 40px;
  text-align: center;
  background: var(--ztc-text-text-1);
  color: var(--ztc-text-text-7);
  transition: all 0.4s;
  display: inline-block;
  border-radius: 50%;
  margin: 0 6px 0 0;
}
.mobile-sidebar.mobile-sidebar3 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul li a:hover {
  background: var(--ztc-bg-bg-7);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
}

.mobile-header.mobile-haeder4 {
  background: var(--ztc-text-text-10);
  position: fixed;
  width: 100%;
}
.mobile-header.mobile-haeder4 .dots-menu {
  color: var(--ztc-text-text-1);
}

.mobile-sidebar.mobile-sidebar4 {
  background: var(--ztc-text-text-10);
}
.mobile-sidebar.mobile-sidebar4 .menu-close {
  color: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar4 .mobile-nav.mobile-nav1 .header-btn7 {
  width: 100%;
  text-align: center;
  margin-top: 24px;
}
.mobile-sidebar.mobile-sidebar4 .mobile-nav.mobile-nav1 span.submenu-button:before {
  background: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar4 .mobile-nav.mobile-nav1 span.submenu-button::after {
  background: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar4 .mobile-nav.mobile-nav1 ul li a {
  color: var(--ztc-text-text-1);
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
}
.mobile-sidebar.mobile-sidebar4 .mobile-nav.mobile-nav1 .allmobilesection h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-1);
  text-transform: capitalize;
  font-weight: var(--ztc-weight-bold);
  margin-top: 24px;
}
.mobile-sidebar.mobile-sidebar4 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single {
  display: flex;
  margin-top: 16px;
}
.mobile-sidebar.mobile-sidebar4 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single .contact-info-icon {
  margin: 0 8px 0 0;
}
.mobile-sidebar.mobile-sidebar4 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single .contact-info-icon i {
  color: var(--ztc-text-text-1);
}
.mobile-sidebar.mobile-sidebar4 .mobile-nav.mobile-nav1 .allmobilesection .footer1-contact-info .contact-info-single .contact-info-text a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  display: inline-block;
}
.mobile-sidebar.mobile-sidebar4 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul {
  margin-top: 20px;
}
.mobile-sidebar.mobile-sidebar4 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul li {
  display: inline-block;
}
.mobile-sidebar.mobile-sidebar4 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul li a {
  height: 40px;
  width: 40px;
  text-align: center;
  background: var(--ztc-text-text-1);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  display: inline-block;
  border-radius: 50%;
  margin: 0 6px 0 0;
}
.mobile-sidebar.mobile-sidebar4 .mobile-nav.mobile-nav1 .allmobilesection .social-links-mobile-menu ul li a:hover {
  background: var(--ztc-bg-bg-11);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
}

/*============= MOBILE MENU CSS AREA ===============*/
/*============= BLOG CSS AREA ===============*/
.blog1-section-area {
  position: relative;
  z-index: 1;
}
.blog1-section-area .blog-header {
  margin-bottom: 60px;
}
.blog1-section-area .blog-auhtor-boxarea {
  position: relative;
  z-index: 1;
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  margin-bottom: 30px;
}
.blog1-section-area .blog-auhtor-boxarea:hover .img1::after {
  height: 100%;
  transition: all 0.4s;
}
.blog1-section-area .blog-auhtor-boxarea:hover .img1 img {
  transform: scale(1.1) rotate(-4deg);
  transition: all 0.4s;
}
.blog1-section-area .blog-auhtor-boxarea:hover .blog-position .heading {
  background: var(--ztc-text-text-2);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.blog1-section-area .blog-auhtor-boxarea .img1 {
  overflow: hidden;
  transition: all 0.4s;
  border-radius: 4px 4px 0 0;
  position: relative;
}
.blog1-section-area .blog-auhtor-boxarea .img1::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-2);
  opacity: 0.7;
}
.blog1-section-area .blog-auhtor-boxarea .img1 img {
  height: 100%;
  width: 100%;
  transition: all 0.4s;
  border-radius: 4px 4px 0 0;
}
.blog1-section-area .blog-auhtor-boxarea .blog-position {
  position: relative;
}
.blog1-section-area .blog-auhtor-boxarea .blog-position .heading {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-3);
  display: inline-block;
  line-height: var(--ztc-font-size-font-s30);
  transition: all 0.4s;
  background: var(--ztc-text-text-1);
  padding: 24px;
  text-align: center;
  margin: -70px 24px 0 24px;
  border-radius: 4px;
  z-index: 2;
  position: relative;
}
.blog1-section-area .blog-auhtor-boxarea .blog-position .blog-content-area {
  padding: 24px;
  text-align: center;
}
.blog1-section-area .blog-auhtor-boxarea .blog-position .blog-content-area ul {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  justify-content: center;
}
@media (max-width: 767px) {
  .blog1-section-area .blog-auhtor-boxarea .blog-position .blog-content-area ul {
    display: inline-block;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog1-section-area .blog-auhtor-boxarea .blog-position .blog-content-area ul {
    display: inline-block;
  }
}
.blog1-section-area .blog-auhtor-boxarea .blog-position .blog-content-area ul li {
  margin: 0 16px 0 0;
}
.blog1-section-area .blog-auhtor-boxarea .blog-position .blog-content-area ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  display: inline-block;
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
}
.blog1-section-area .blog-auhtor-boxarea .blog-position .blog-content-area ul li a i {
  margin: 0 4px 0 0;
}
@media (max-width: 767px) {
  .blog1-section-area .blog-auhtor-boxarea .blog-position .blog-content-area ul li:nth-child(2) {
    margin-top: 10px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog1-section-area .blog-auhtor-boxarea .blog-position .blog-content-area ul li:nth-child(2) {
    margin-top: 10px;
  }
}
.blog1-section-area .blog-auhtor-boxarea .blog-position .blog-content-area p {
  font-size: var(--ztc-font-size-font-s18);
  font-family: var(--ztc-family-font1);
  line-height: var(--ztc-font-size-font-s26);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
  transition: all 0.4s;
}
.blog1-section-area .blog-auhtor-boxarea .blog-position .blog-content-area .learnmore {
  display: inline-block;
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  line-height: var(--ztc-font-size-font-s16);
  margin-top: 24px;
}
.blog1-section-area .blog-auhtor-boxarea .blog-position .blog-content-area .learnmore:hover {
  color: var(--ztc-text-text-2);
  transition: all 0.4s;
}
.blog1-section-area .blog-auhtor-boxarea .blog-position .blog-content-area .learnmore i {
  margin-left: 4px;
  transform: rotate(-45deg);
}
.blog1-section-area .pagination-area ul {
  text-align: center;
  justify-content: center;
  margin-top: 30px;
}
.blog1-section-area .pagination-area ul li a {
  height: 50px;
  width: 50px;
  display: inline-block;
  border-radius: 4px !important;
  transition: all 0.4s;
  border: none;
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s40);
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-3);
  margin: 0 16px;
  box-shadow: none;
  background: var(--ztc-bg-bg-1);
}
.blog1-section-area .pagination-area ul li a:hover {
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
}
.blog1-section-area .pagination-area ul li .page-link.active {
  background: var(--ztc-text-text-2) !important;
  color: var(--ztc-text-text-1);
}

.blog2-section-area {
  position: relative;
  z-index: 1;
}
.blog2-section-area .blog-header {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .blog2-section-area .blog-header {
    margin-bottom: 30px;
  }
}
.blog2-section-area .blog-box2 {
  position: relative;
  z-index: 1;
  border-radius: 4px;
  margin-bottom: 30px;
}
.blog2-section-area .blog-box2:hover .img1::after {
  height: 100%;
  width: 404px;
  transition: all 0.4s;
}
.blog2-section-area .blog-box2:hover .img1 img {
  transform: scale(1.1) rotate(0);
  transition: all 0.4s;
}
.blog2-section-area .blog-box2 .img1 {
  position: relative;
  z-index: 1;
  transition: all 0.4s;
}
.blog2-section-area .blog-box2 .img1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-3);
  opacity: 0.7;
}
.blog2-section-area .blog-box2 .img1 img {
  width: 385px;
  height: 288px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 4px;
}
.blog2-section-area .blog-box2 .img1 {
  overflow: hidden;
  transition: all 0.4s;
  border-radius: 4px 4px 0 0;
  position: relative;
}
.blog2-section-area .blog-box2 .img1::after {
  position: absolute;
  content: "";
  height: 0;
  width: 404px;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-3);
  opacity: 0.7;
}
.blog2-section-area .blog-box2 .img1 img {
  width: 385px;
  height: 288px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 4px;
}
.blog2-section-area .blog-box2 .blog-content-area {
  padding: 32px;
  background: var(--ztc-text-text-1);
  box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
  border-radius: 4px;
  position: absolute;
  z-index: 1;
  top: 12%;
  right: 0;
  width: 430px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog2-section-area .blog-box2 .blog-content-area {
    position: relative;
    top: 0;
    width: 100%;
    margin-top: -115px;
    border-radius: 0 0 4px 4px;
  }
}
@media (max-width: 767px) {
  .blog2-section-area .blog-box2 .blog-content-area {
    position: relative;
    top: 0;
    width: 100%;
    margin-top: -115px;
    border-radius: 0 0 4px 4px;
  }
}
.blog2-section-area .blog-box2 .blog-content-area .heading {
  display: block;
  font-size: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-5);
  line-height: var(--ztc-font-size-font-s30);
  padding-bottom: 24px;
  border-bottom: 1px solid #E7E9EB;
  transition: all 0.4s;
}
.blog2-section-area .blog-box2 .blog-content-area .heading:hover {
  color: var(--ztc-bg-bg-3);
  transition: all 0.4s;
}
.blog2-section-area .blog-box2 .blog-content-area ul {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .blog2-section-area .blog-box2 .blog-content-area ul {
    display: inline-block;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog2-section-area .blog-box2 .blog-content-area ul {
    display: inline-block;
  }
}
.blog2-section-area .blog-box2 .blog-content-area ul li {
  margin: 0 16px 0 0;
}
.blog2-section-area .blog-box2 .blog-content-area ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  display: inline-block;
  color: var(--ztc-text-text-6);
  transition: all 0.4s;
}
.blog2-section-area .blog-box2 .blog-content-area ul li a i {
  margin: 0 4px 0 0;
  color: var(--ztc-text-text-5);
}
@media (max-width: 767px) {
  .blog2-section-area .blog-box2 .blog-content-area ul li:nth-child(2) {
    margin-top: 10px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog2-section-area .blog-box2 .blog-content-area ul li:nth-child(2) {
    margin-top: 10px;
  }
}
.blog2-section-area .blog-box2 .blog-content-area p {
  font-size: var(--ztc-font-size-font-s18);
  font-family: var(--ztc-family-font1);
  line-height: var(--ztc-font-size-font-s26);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
  transition: all 0.4s;
}
.blog2-section-area .blog-box2 .blog-content-area .learnmore {
  display: inline-block;
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  line-height: var(--ztc-font-size-font-s16);
  margin-top: 24px;
}
.blog2-section-area .blog-box2 .blog-content-area .learnmore:hover {
  color: var(--ztc-bg-bg-3);
  transition: all 0.4s;
}
.blog2-section-area .blog-box2 .blog-content-area .learnmore i {
  margin-left: 4px;
  transform: rotate(-45deg);
}
.blog2-section-area .blog-auhtor-boxarea {
  position: relative;
  z-index: 1;
  border-radius: 4px;
  margin-bottom: 30px;
}
.blog2-section-area .blog-auhtor-boxarea:hover .img1::after {
  height: 100%;
  transition: all 0.4s;
}
.blog2-section-area .blog-auhtor-boxarea:hover .img1 img {
  transform: scale(1.1) rotate(-4deg);
  transition: all 0.4s;
}
.blog2-section-area .blog-auhtor-boxarea .img1 {
  overflow: hidden;
  transition: all 0.4s;
  border-radius: 4px 4px 0 0;
  position: relative;
}
.blog2-section-area .blog-auhtor-boxarea .img1::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-3);
  opacity: 0.7;
}
.blog2-section-area .blog-auhtor-boxarea .img1 img {
  height: 100%;
  width: 100%;
  transition: all 0.4s;
  border-radius: 4px 4px 0 0;
}
.blog2-section-area .blog-auhtor-boxarea .blog-content-area {
  padding: 32px;
  background: var(--ztc-text-text-1);
  box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
  border-radius: 4px;
  margin: -115px 24px 0 24px;
  position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog2-section-area .blog-auhtor-boxarea .blog-content-area {
    position: relative;
    top: 0;
    width: 100%;
    margin: -115px 0 0 0;
    border-radius: 0 0 4px 4px;
  }
}
@media (max-width: 767px) {
  .blog2-section-area .blog-auhtor-boxarea .blog-content-area {
    position: relative;
    top: 0;
    width: 100%;
    margin: -115px 0 0 0;
    border-radius: 0 0 4px 4px;
  }
}
.blog2-section-area .blog-auhtor-boxarea .blog-content-area .heading {
  display: block;
  font-size: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-5);
  line-height: var(--ztc-font-size-font-s30);
  padding-bottom: 24px;
  border-bottom: 1px solid #E7E9EB;
  transition: all 0.4s;
}
.blog2-section-area .blog-auhtor-boxarea .blog-content-area .heading:hover {
  color: var(--ztc-bg-bg-3);
  transition: all 0.4s;
}
.blog2-section-area .blog-auhtor-boxarea .blog-content-area ul {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
@media (max-width: 767px) {
  .blog2-section-area .blog-auhtor-boxarea .blog-content-area ul {
    display: inline-block;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog2-section-area .blog-auhtor-boxarea .blog-content-area ul {
    display: inline-block;
  }
}
.blog2-section-area .blog-auhtor-boxarea .blog-content-area ul li {
  margin: 0 16px 0 0;
}
.blog2-section-area .blog-auhtor-boxarea .blog-content-area ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  display: inline-block;
  color: var(--ztc-text-text-6);
  transition: all 0.4s;
}
.blog2-section-area .blog-auhtor-boxarea .blog-content-area ul li a i {
  margin: 0 4px 0 0;
  color: var(--ztc-text-text-5);
}
@media (max-width: 767px) {
  .blog2-section-area .blog-auhtor-boxarea .blog-content-area ul li:nth-child(2) {
    margin-top: 10px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog2-section-area .blog-auhtor-boxarea .blog-content-area ul li:nth-child(2) {
    margin-top: 10px;
  }
}
.blog2-section-area .blog-auhtor-boxarea .blog-content-area p {
  font-size: var(--ztc-font-size-font-s18);
  font-family: var(--ztc-family-font1);
  line-height: var(--ztc-font-size-font-s26);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
  transition: all 0.4s;
}
.blog2-section-area .blog-auhtor-boxarea .blog-content-area .learnmore {
  display: inline-block;
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  line-height: var(--ztc-font-size-font-s16);
  margin-top: 24px;
}
.blog2-section-area .blog-auhtor-boxarea .blog-content-area .learnmore:hover {
  color: var(--ztc-bg-bg-3);
  transition: all 0.4s;
}
.blog2-section-area .blog-auhtor-boxarea .blog-content-area .learnmore i {
  margin-left: 4px;
  transform: rotate(-45deg);
}

.blog3-section-area .blog-header-area {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .blog3-section-area .blog-header-area {
    margin-bottom: 30px;
  }
}
.blog3-section-area .blog-boxes-area {
  background: var(--ztc-bg-bg-1);
  transition: all 0.4s;
  overflow: hidden;
  border-radius: 4px;
  padding: 24px;
  position: relative;
  margin-bottom: 30px;
}
.blog3-section-area .blog-boxes-area:hover {
  transform: translateY(-5px);
  transition: all 0.4s;
}
.blog3-section-area .blog-boxes-area:hover .img1::after {
  height: 100%;
  transition: all 0.4s;
}
.blog3-section-area .blog-boxes-area:hover .img1 img {
  transform: scale(1.1) rotate(-4deg);
  transition: all 0.4s;
}
.blog3-section-area .blog-boxes-area .tags-area {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.blog3-section-area .blog-boxes-area .tags-area .date {
  margin: 0 20px 0 0;
}
.blog3-section-area .blog-boxes-area .tags-area .date a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-8);
  font-weight: var(--ztc-weight-medium);
  transition: all 0.4s;
}
.blog3-section-area .blog-boxes-area .tags-area .date a i {
  color: var(--ztc-text-text-7);
  margin: 0 4px 0 0;
}
.blog3-section-area .blog-boxes-area .tags-area .date a img {
  margin: 0 4px 0 0;
}
.blog3-section-area .blog-boxes-area .tags-area .contact a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-8);
  font-weight: var(--ztc-weight-medium);
  transition: all 0.4s;
}
.blog3-section-area .blog-boxes-area .tags-area .contact a img {
  margin: 0 4px 0 0;
}
.blog3-section-area .blog-boxes-area .content-area a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s32);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-7);
  display: block;
  transition: all 0.4s;
  margin-bottom: 24px;
}
.blog3-section-area .blog-boxes-area .content-area a:hover {
  color: var(--ztc-bg-bg-7);
  transition: all 0.4s;
}
.blog3-section-area .blog-boxes-area .content-area .readmore {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-7);
  transition: all 0.4s;
  display: inline-block;
}
.blog3-section-area .blog-boxes-area .content-area .readmore:hover {
  color: var(--ztc-bg-bg-7);
  transition: all 0.4s;
}
.blog3-section-area .blog-boxes-area .img1 {
  position: relative;
  border-radius: 4px;
  transition: all 0.4s;
  overflow: hidden;
}
.blog3-section-area .blog-boxes-area .img1::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-7);
  opacity: 0.7;
  border-radius: 4px;
}
.blog3-section-area .blog-boxes-area .img1 img {
  height: 100%;
  width: 100%;
  transition: all 0.4s;
  border-radius: 4px;
}

.blog4-section-area {
  position: relative;
  z-index: 1;
}
.blog4-section-area .blog-header-area {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .blog4-section-area .blog-header-area {
    margin-bottom: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog4-section-area .blog-header-area {
    margin-bottom: 30px;
  }
}
.blog4-section-area .blog-auhtor-boxesarea {
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  margin-bottom: 30px;
}
.blog4-section-area .blog-auhtor-boxesarea:hover .img1::after {
  height: 100%;
  transition: all 0.4s;
}
.blog4-section-area .blog-auhtor-boxesarea:hover .img1 img {
  transform: scale(1.1) rotate(-4deg);
  transition: all 0.4s;
}
.blog4-section-area .blog-auhtor-boxesarea .img1 {
  overflow: hidden;
  border-radius: 4px 4px 0 0;
  transition: all 0.4s;
  position: relative;
}
.blog4-section-area .blog-auhtor-boxesarea .img1::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-11);
  opacity: 0.7;
  border-radius: 4px 4px 0 0;
}
.blog4-section-area .blog-auhtor-boxesarea .img1 img {
  height: 100%;
  width: 100%;
  border-radius: 4px 4px 0 0;
  text-transform: all 0.4s;
}
.blog4-section-area .blog-auhtor-boxesarea .content-area {
  padding: 24px;
}
.blog4-section-area .blog-auhtor-boxesarea .content-area a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s32);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-10);
  transition: all 0.4s;
  display: inline-block;
}
@media (max-width: 767px) {
  .blog4-section-area .blog-auhtor-boxesarea .content-area a {
    font-size: var(--ztc-font-size-font-s20);
    line-height: var(--ztc-font-size-font-s26);
  }
}
.blog4-section-area .blog-auhtor-boxesarea .content-area a:hover {
  color: var(--ztc-bg-bg-11);
  transition: all 0.4s;
}
.blog4-section-area .blog-auhtor-boxesarea .content-area p {
  font-size: var(--ztc-font-size-font-s18);
  font-family: var(--ztc-family-font1);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-11);
  transition: all 0.4s;
  padding-top: 16px;
  padding-bottom: 24px;
  border-bottom: 1px solid #DBE3EF;
}
.blog4-section-area .blog-auhtor-boxesarea .content-area ul {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 24px;
}
.blog4-section-area .blog-auhtor-boxesarea .content-area ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-11);
  transition: all 0.4s;
  display: inline-block;
  margin-bottom: 0;
}
.blog4-section-area .blog-auhtor-boxesarea .content-area ul li a i {
  color: var(--ztc-text-text-10);
  margin: 0 6px 0 0;
}
.blog4-section-area .blog-auhtor-boxesarea .content-area ul li .learnmore {
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-10);
  transition: all 0.4s;
  display: inline-block;
}
.blog4-section-area .blog-auhtor-boxesarea .content-area ul li .learnmore:hover {
  color: var(--ztc-bg-bg-11);
  transition: all 0.4s;
}
.blog4-section-area .blog-auhtor-boxesarea .content-area ul li .learnmore i {
  transform: rotate(-45deg);
}

.blog-leftside-section-area .blog-auhtor-side-area .search-boxarea {
  padding: 32px 20px;
  border-radius: 4px;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-1);
}
.blog-leftside-section-area .blog-auhtor-side-area .search-boxarea h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s30);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 4px;
  color: var(--ztc-text-text-3);
}
.blog-leftside-section-area .blog-auhtor-side-area .search-boxarea form {
  margin-top: 20px;
  position: relative;
  z-index: 1;
  background: var(--ztc-text-text-1);
  border-radius: 4px;
  padding: 16px;
  height: 50px;
}
.blog-leftside-section-area .blog-auhtor-side-area .search-boxarea form input {
  width: 100%;
  background: none;
  outline: none;
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-3);
  line-height: var(--ztc-font-size-font-s16);
}
.blog-leftside-section-area .blog-auhtor-side-area .search-boxarea form input::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
  line-height: var(16px);
}
.blog-leftside-section-area .blog-auhtor-side-area .search-boxarea form input::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
  line-height: var(16px);
}
.blog-leftside-section-area .blog-auhtor-side-area .search-boxarea form button {
  border: none;
  outline: none;
  background: var(--ztc-text-text-2);
  color: var(--ztc-text-text-1);
  height: 50px;
  width: 56px;
  text-align: center;
  line-height: 50px;
  display: inline-block;
  border-radius: 4px;
  transition: all 0.4s;
  position: absolute;
  right: 0;
  top: 0;
}
.blog-leftside-section-area .blog-auhtor-side-area .search-boxarea form button:hover {
  background: var(--ztc-text-text-3);
  transition: all 0.4s;
}
.blog-leftside-section-area .blog-auhtor-side-area .blog-author-list {
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  transition: all 0.4s;
  padding: 32px 20px;
  margin-top: 32px;
}
.blog-leftside-section-area .blog-auhtor-side-area .blog-author-list h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  margin-bottom: 4px;
}
.blog-leftside-section-area .blog-auhtor-side-area .blog-author-list ul li a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  font-family: var(--ztc-family-font1);
  padding: 20px 24px;
  background: var(--ztc-text-text-1);
  margin-top: 20px;
  border-radius: 4px;
  height: 60px;
  transition: all 0.4s;
}
.blog-leftside-section-area .blog-auhtor-side-area .blog-author-list ul li a i {
  transition: all 0.4s;
}
.blog-leftside-section-area .blog-auhtor-side-area .blog-author-list ul li a:hover {
  background: var(--ztc-text-text-2);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.blog-leftside-section-area .blog-auhtor-side-area .blog-author-list ul li a:hover i {
  transform: rotate(90deg);
  transition: all 0.4s;
}
.blog-leftside-section-area .blog-auhtor-side-area .recent-posts-area {
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  margin-top: 32px;
  padding: 32px 24px;
}
.blog-leftside-section-area .blog-auhtor-side-area .recent-posts-area h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s30);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 4px;
  color: var(--ztc-text-text-3);
}
.blog-leftside-section-area .blog-auhtor-side-area .recent-posts-area .recent-single-posts {
  padding-top: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #E7E9EB;
}
.blog-leftside-section-area .blog-auhtor-side-area .recent-posts-area .recent-single-posts .img1 {
  position: absolute;
}
.blog-leftside-section-area .blog-auhtor-side-area .recent-posts-area .recent-single-posts .img1 img {
  height: 80px;
  width: 80px;
  border-radius: 4px;
  -o-object-fit: cover;
     object-fit: cover;
}
.blog-leftside-section-area .blog-auhtor-side-area .recent-posts-area .recent-single-posts .content {
  padding-left: 100px;
}
.blog-leftside-section-area .blog-auhtor-side-area .recent-posts-area .recent-single-posts .content a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
  display: inline-block;
}
.blog-leftside-section-area .blog-auhtor-side-area .recent-posts-area .recent-single-posts .content a i {
  color: var(--ztc-text-text-3);
  margin: 0 6px 0 0;
}
.blog-leftside-section-area .blog-auhtor-side-area .recent-posts-area .recent-single-posts .content .heading {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  display: inline-block;
  margin-top: 10px;
}
.blog-leftside-section-area .blog-auhtor-side-area .recent-posts-area .recent-single-posts .content .heading:hover {
  color: var(--ztc-text-text-2);
  transition: all 0.4s;
}
.blog-leftside-section-area .blog-auhtor-side-area .helping-area {
  background: var(--ztc-text-text-2);
  border-radius: 4px;
  margin-top: 32px;
  padding: 32px 24px;
}
.blog-leftside-section-area .blog-auhtor-side-area .helping-area h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s30);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 4px;
  color: var(--ztc-text-text-1);
}
.blog-leftside-section-area .blog-auhtor-side-area .helping-area .btn-area {
  margin-top: 24px;
}
.blog-leftside-section-area .blog-auhtor-side-area .helping-area .btn-area .header-btn1 {
  background: var(--ztc-text-text-1);
  color: var(--ztc-text-text-3);
}
.blog-leftside-section-area .blog-auhtor-side-area .helping-area .btn-area .header-btn1 i {
  margin: 0 6px 0 0;
}
.blog-leftside-section-area .blog-auhtor-side-area .tags-area {
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  margin-top: 32px;
  padding: 32px 24px;
}
.blog-leftside-section-area .blog-auhtor-side-area .tags-area h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s30);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 4px;
  color: var(--ztc-text-text-3);
}
.blog-leftside-section-area .blog-auhtor-side-area .tags-area ul li {
  display: inline-block;
}
.blog-leftside-section-area .blog-auhtor-side-area .tags-area ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  display: inline-block;
  background: var(--ztc-text-text-1);
  border-radius: 4px;
  padding: 8px 12px;
  margin: 12px 12px 0 0;
}
.blog-leftside-section-area .blog-auhtor-side-area .tags-area ul li a:hover {
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  transform: translateY(-3px);
  color: var(--ztc-text-text-1);
}
.blog-leftside-section-area .blog-auhtor-side-area .download-broucher {
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  margin-top: 32px;
  padding: 32px 24px;
}
.blog-leftside-section-area .blog-auhtor-side-area .download-broucher h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s30);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 4px;
  color: var(--ztc-text-text-3);
}
.blog-leftside-section-area .blog-auhtor-side-area .download-broucher p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
  margin-top: 16px;
}
.blog-leftside-section-area .blog-auhtor-side-area .download-broucher .btn-area .header-btn1 {
  margin-top: 24px;
}
.blog-leftside-section-area .blog-auhtor-side-area .download-broucher .btn-area .header-btn1 img {
  filter: brightness(0) invert(1);
  transition: all 0.4s;
  margin: -5px 4px 0 0;
}
.blog-leftside-section-area .blog-auhtor-side-area .download-broucher .btn-area .header-btn1:hover {
  background: var(--ztc-text-text-3);
  transition: all 0.4s;
  transform: translateY(-5px);
  color: var(--ztc-text-text-1);
}
.blog-leftside-section-area .blog-auhtor-side-area .download-broucher .btn-area .header-btn2 {
  background: var(--ztc-text-text-1);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  margin-left: 16px;
  margin-top: 24px;
  padding: 16px 20px;
}
@media (max-width: 767px) {
  .blog-leftside-section-area .blog-auhtor-side-area .download-broucher .btn-area .header-btn2 {
    margin-left: 0;
  }
}
.blog-leftside-section-area .blog-auhtor-side-area .download-broucher .btn-area .header-btn2 img {
  transition: all 0.4s;
  margin: -5px 4px 0 0;
}
.blog-leftside-section-area .blog-auhtor-side-area .download-broucher .btn-area .header-btn2:hover {
  background: var(--ztc-text-text-3);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.blog-leftside-section-area .blog-auhtor-side-area .download-broucher .btn-area .header-btn2:hover img {
  transition: all 0.4s;
  filter: brightness(0) invert(1);
}
.blog-leftside-section-area .blog-auhtor-side-area .social-icons {
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  margin-top: 32px;
  padding: 32px 24px;
}
.blog-leftside-section-area .blog-auhtor-side-area .social-icons h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s30);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 4px;
  color: var(--ztc-text-text-3);
}
.blog-leftside-section-area .blog-auhtor-side-area .social-icons ul {
  margin-top: 24px;
}
.blog-leftside-section-area .blog-auhtor-side-area .social-icons ul li {
  display: inline-block;
}
.blog-leftside-section-area .blog-auhtor-side-area .social-icons ul li a {
  display: inline-block;
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  background: var(--ztc-text-text-1);
  transition: all 0.4s;
  color: var(--ztc-text-text-3);
  margin: 0 8px 0 0;
}
.blog-leftside-section-area .blog-auhtor-side-area .social-icons ul li a:hover {
  background: var(--ztc-text-text-2);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.blog-leftside-section-area .blog-leftside-area {
  padding: 0 0 0 80px;
}
@media (max-width: 767px) {
  .blog-leftside-section-area .blog-leftside-area {
    padding: 0;
    margin-top: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-leftside-section-area .blog-leftside-area {
    padding: 0;
    margin-top: 30px;
  }
}
.blog-leftside-section-area .blog-leftside-area .heading2 p {
  margin-bottom: 0 !important;
}
.blog-leftside-section-area .blog-leftside-area .heading2 h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s32);
  line-height: var(--ztc-font-size-font-s40);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  font-weight: var(--ztc-weight-bold);
}
.blog-leftside-section-area .blog-leftside-area .blog-left-heading .img1 img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
}
.blog-leftside-section-area .blog-leftside-area .tags-share-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 767px) {
  .blog-leftside-section-area .blog-leftside-area .tags-share-area {
    display: inline-block;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-leftside-section-area .blog-leftside-area .tags-share-area {
    display: inline-block;
  }
}
.blog-leftside-section-area .blog-leftside-area .tags-share-area .tags {
  display: flex;
  align-items: center;
}
@media (max-width: 767px) {
  .blog-leftside-section-area .blog-leftside-area .tags-share-area .tags {
    display: inline-block;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-leftside-section-area .blog-leftside-area .tags-share-area .tags {
    margin-bottom: 20px;
  }
}
.blog-leftside-section-area .blog-leftside-area .tags-share-area .tags h4 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  margin: 0 8px 0 0;
}
@media (max-width: 767px) {
  .blog-leftside-section-area .blog-leftside-area .tags-share-area .tags ul {
    margin-bottom: 30px;
  }
}
.blog-leftside-section-area .blog-leftside-area .tags-share-area .tags ul li {
  display: inline-block;
}
.blog-leftside-section-area .blog-leftside-area .tags-share-area .tags ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  color: var(--ztc-text-text-2);
  font-weight: var(--ztc-weight-medium);
  display: inline-block;
  padding: 8px 12px;
  text-transform: capitalize;
  border: 1px solid var(--ztc-text-text-2);
  border-radius: 4px;
  margin: 0 10px 0 0;
  transition: all 0.4s;
}
@media (max-width: 767px) {
  .blog-leftside-section-area .blog-leftside-area .tags-share-area .tags ul li a {
    margin: 10px 4px 0 0;
  }
}
.blog-leftside-section-area .blog-leftside-area .tags-share-area .tags ul li a:hover {
  background: var(--ztc-text-text-2);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.blog-leftside-section-area .blog-leftside-area .tags-share-area .share {
  display: flex;
  align-items: center;
}
.blog-leftside-section-area .blog-leftside-area .tags-share-area .share h4 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  margin: 0 8px 0 0;
}
.blog-leftside-section-area .blog-leftside-area .tags-share-area .share ul li {
  display: inline-block;
}
.blog-leftside-section-area .blog-leftside-area .tags-share-area .share ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s36);
  color: var(--ztc-text-text-2);
  font-weight: var(--ztc-weight-medium);
  display: inline-block;
  text-transform: capitalize;
  border-radius: 4px;
  margin: 0 10px 0 0;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-1);
  border-radius: 50%;
  height: 36px;
  width: 36px;
  text-align: center;
}
.blog-leftside-section-area .blog-leftside-area .tags-share-area .share ul li a:hover {
  background: var(--ztc-text-text-2);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.blog-leftside-section-area .blog-leftside-area .comments-boxarea {
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  padding: 24px;
}
.blog-leftside-section-area .blog-leftside-area .comments-boxarea .comment-auhtor-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 767px) {
  .blog-leftside-section-area .blog-leftside-area .comments-boxarea .comment-auhtor-box {
    display: inline-block;
  }
}
.blog-leftside-section-area .blog-leftside-area .comments-boxarea .comment-auhtor-box .all-content {
  display: flex;
  align-items: center;
}
.blog-leftside-section-area .blog-leftside-area .comments-boxarea .comment-auhtor-box .all-content .img1 img {
  height: 70px;
  width: 70px;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}
.blog-leftside-section-area .blog-leftside-area .comments-boxarea .comment-auhtor-box .all-content .content-area {
  margin-left: 16px;
}
.blog-leftside-section-area .blog-leftside-area .comments-boxarea .comment-auhtor-box .all-content .content-area a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s20);
  line-height: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  display: block;
}
.blog-leftside-section-area .blog-leftside-area .comments-boxarea .comment-auhtor-box .all-content .content-area .date {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
  line-height: var(--ztc-font-size-font-s18);
  display: inline-block;
  margin-top: 10px;
}
@media (max-width: 767px) {
  .blog-leftside-section-area .blog-leftside-area .comments-boxarea .comment-auhtor-box .reply {
    margin-top: 20px;
  }
}
.blog-leftside-section-area .blog-leftside-area .comments-boxarea .comment-auhtor-box .reply a {
  display: inline-block;
  font-size: var(--ztc-font-size-font-s18);
  font-family: var(--ztc-family-font1);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  cursor: pointer;
}
.blog-leftside-section-area .blog-leftside-area .comments-boxarea .comment-auhtor-box .reply a:hover {
  color: var(--ztc-text-text-2);
  transition: all 0.4s;
}
.blog-leftside-section-area .blog-leftside-area .comments-boxarea .comment-auhtor-box .reply a:hover img {
  filter: none;
  transition: all 0.4s;
}
.blog-leftside-section-area .blog-leftside-area .comments-boxarea .comment-auhtor-box .reply a img {
  filter: brightness(0);
  transition: all 0.4s;
  margin: 0 4px 0 0;
}
.blog-leftside-section-area .blog-leftside-area .comments-boxarea.boxarea2 {
  margin: 0 0 0 30px;
}
@media (max-width: 767px) {
  .blog-leftside-section-area .blog-leftside-area .comments-boxarea.boxarea2 {
    margin: 0;
  }
}
.blog-leftside-section-area .blog-leftside-area .contact-submit-boxarea {
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  padding: 32px;
}
.blog-leftside-section-area .blog-leftside-area .contact-submit-boxarea h4 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s32);
  line-height: var(--ztc-font-size-font-s32);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
}
.blog-leftside-section-area .blog-leftside-area .contact-submit-boxarea p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s24);
  margin-top: 16px;
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
}
.blog-leftside-section-area .blog-leftside-area .contact-submit-boxarea .input-area {
  margin-top: 20px;
}
.blog-leftside-section-area .blog-leftside-area .contact-submit-boxarea .input-area input {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-3);
  background: var(--ztc-text-text-1);
  width: 100%;
  border-radius: 4px;
  padding: 16px 20px;
  height: 58px;
}
.blog-leftside-section-area .blog-leftside-area .contact-submit-boxarea .input-area input::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
}
.blog-leftside-section-area .blog-leftside-area .contact-submit-boxarea .input-area input::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
}
.blog-leftside-section-area .blog-leftside-area .contact-submit-boxarea .input-area textarea {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-3);
  background: var(--ztc-text-text-1);
  width: 100%;
  border-radius: 4px;
  padding: 16px 20px;
  height: 140px;
}
.blog-leftside-section-area .blog-leftside-area .contact-submit-boxarea .input-area textarea::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
}
.blog-leftside-section-area .blog-leftside-area .contact-submit-boxarea .input-area textarea::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
}
.blog-leftside-section-area .blog-leftside-area .contact-submit-boxarea .input-area1 {
  text-align: end;
}
.blog-leftside-section-area .blog-leftside-area .contact-submit-boxarea .input-area1 button {
  border: none;
  margin-top: 30px;
}
.blog-leftside-section-area .blog-leftside-area.blog-rightside {
  padding: 0 70px 0 0 !important;
}
@media (max-width: 767px) {
  .blog-leftside-section-area .blog-leftside-area.blog-rightside {
    padding: 0 !important;
    margin-bottom: 30px;
    margin-top: 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .blog-leftside-section-area .blog-leftside-area.blog-rightside {
    padding: 0 !important;
    margin-bottom: 30px;
    margin-top: 0;
  }
}
.blog-leftside-section-area .blog-leftside-area.blog-singleside {
  padding: 0;
}

/*============= BLOG CSS AREA ENDS ===============*/
/*============= FOOTER CSS AREA ===============*/
.footer1-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-text-text-3);
}
.footer1-section-area .footer-logo-content {
  border-bottom: 1px solid #1A1F26;
}
.footer1-section-area .footer-logo-content .logo-content p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  margin-top: 24px;
}
.footer1-section-area .footer-logo-content .logo-content ul {
  margin-top: 24px;
}
.footer1-section-area .footer-logo-content .logo-content ul li {
  display: inline-block;
}
.footer1-section-area .footer-logo-content .logo-content ul li a {
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  display: inline-block;
  transition: all 0.4s;
  background: #1A1E26;
  border-radius: 50%;
  color: var(--ztc-text-text-1);
  margin: 0 6px 0 0;
}
.footer1-section-area .footer-logo-content .logo-content ul li a:hover {
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
  transform: translateY(-3px);
}
.footer1-section-area .footer-logo-content .service-heading h2 {
  font-family: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-1);
  font-size: var(--ztc-font-size-font-s20);
  text-transform: capitalize;
  margin-bottom: 4px;
}
.footer1-section-area .footer-logo-content .service-heading ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  transition: all 0.4s;
  display: inline-block;
  padding-top: 20px;
}
.footer1-section-area .footer-logo-content .service-heading ul li a:hover {
  color: var(--ztc-text-text-2);
  transition: all 0.4s;
  padding-left: 5px;
}
.footer1-section-area .footer-logo-content .service-heading.contact ul li a {
  display: flex;
}
.footer1-section-area .footer-logo-content .service-heading.contact ul li a:hover .icons {
  color: var(--ztc-text-text-2);
  transition: all 0.4s;
}
.footer1-section-area .footer-logo-content .service-heading.contact ul li a span.icons {
  height: 30px;
  width: 30px;
  text-align: center;
  line-height: 30px;
  border-radius: 50%;
  background: #01060E;
  transition: all 0.4s;
  display: inline-block;
  margin: 0 8px 0 0;
}
.footer1-section-area .copyright p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  line-height: var(--ztc-font-size-font-s24);
  text-align: center;
  padding: 24px 0 30px 0;
}

.footer2-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-text-text-5);
}
.footer2-section-area .cta-header {
  padding-top: 100px;
}
@media (max-width: 767px) {
  .footer2-section-area .cta-header {
    padding-top: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer2-section-area .cta-header {
    padding-top: 50px;
  }
}
.footer2-section-area .cta-header h2 {
  color: var(--ztc-text-text-1);
}
.footer2-section-area .cta-header p {
  color: var(--ztc-text-text-1);
  opacity: 0.8 !important;
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s26);
}
.footer2-section-area .cta-header form {
  background: var(--ztc-text-text-1);
  border-radius: 4px;
  padding: 20px;
  position: relative;
  height: 56px;
  margin-top: 32px;
  display: inline-block;
  width: 385px;
}
@media (max-width: 767px) {
  .footer2-section-area .cta-header form {
    width: 100%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer2-section-area .cta-header form {
    width: 100%;
  }
}
.footer2-section-area .cta-header form input {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-5);
  font-weight: var(--ztc-weight-medium);
  width: 100%;
  outline: none;
}
.footer2-section-area .cta-header form input::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
}
.footer2-section-area .cta-header form input::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
}
.footer2-section-area .cta-header form button {
  border: none;
  transition: all 0.4s;
  position: absolute;
  right: 4px;
  top: 4px;
}
.footer2-section-area .footer-logo-content {
  border-bottom: 1px solid #2C3C4B;
}
.footer2-section-area .footer-logo-content .logo-content p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  margin-top: 24px;
}
.footer2-section-area .footer-logo-content .logo-content ul {
  margin-top: 24px;
}
.footer2-section-area .footer-logo-content .logo-content ul li {
  display: inline-block;
}
.footer2-section-area .footer-logo-content .logo-content ul li a {
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  display: inline-block;
  transition: all 0.4s;
  background: #2C3B4A;
  border-radius: 50%;
  color: var(--ztc-text-text-1);
  margin: 0 6px 0 0;
}
.footer2-section-area .footer-logo-content .logo-content ul li a:hover {
  background: var(--ztc-bg-bg-3);
  transition: all 0.4s;
  color: var(--ztc-text-text-5);
  transform: translateY(-3px);
}
.footer2-section-area .footer-logo-content .service-heading h2 {
  font-family: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-1);
  font-size: var(--ztc-font-size-font-s20);
  text-transform: capitalize;
  margin-bottom: 4px;
}
.footer2-section-area .footer-logo-content .service-heading ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 0.8 !important;
  transition: all 0.4s;
  display: inline-block;
  padding-top: 20px;
}
.footer2-section-area .footer-logo-content .service-heading ul li a:hover {
  color: var(--ztc-bg-bg-3);
  transition: all 0.4s;
  padding-left: 5px;
}
.footer2-section-area .footer-logo-content .service-heading.contact ul li a {
  display: flex;
}
.footer2-section-area .footer-logo-content .service-heading.contact ul li a:hover .icons {
  color: var(--ztc-bg-bg-3);
  transition: all 0.4s;
}
.footer2-section-area .footer-logo-content .service-heading.contact ul li a .icons {
  margin: 0 8px 0 0;
  transition: all 0.4s;
}
.footer2-section-area .copyright p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  line-height: var(--ztc-font-size-font-s24);
  text-align: center;
  padding: 24px 0 30px 0;
}

.footer3-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-text-text-7);
}
.footer3-section-area .cta-header {
  padding-top: 100px;
}
@media (max-width: 767px) {
  .footer3-section-area .cta-header {
    padding-top: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer3-section-area .cta-header {
    padding-top: 50px;
  }
}
.footer3-section-area .cta-header h2 {
  color: var(--ztc-text-text-1);
}
.footer3-section-area .cta-header p {
  color: var(--ztc-text-text-1);
  opacity: 0.8 !important;
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s26);
}
.footer3-section-area .cta-header form {
  background: var(--ztc-text-text-1);
  border-radius: 4px;
  padding: 20px;
  position: relative;
  height: 56px;
  margin-top: 32px;
  display: inline-block;
  width: 385px;
}
@media (max-width: 767px) {
  .footer3-section-area .cta-header form {
    width: 100%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .footer3-section-area .cta-header form {
    width: 100%;
  }
}
.footer3-section-area .cta-header form input {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-5);
  font-weight: var(--ztc-weight-medium);
  width: 100%;
  outline: none;
}
.footer3-section-area .cta-header form input::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
}
.footer3-section-area .cta-header form input::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
}
.footer3-section-area .cta-header form button {
  border: none;
  transition: all 0.4s;
  position: absolute;
  right: 4px;
  top: 4px;
}
.footer3-section-area .footer-logo-content {
  border-bottom: 1px solid #221D19;
}
.footer3-section-area .footer-logo-content .logo-content p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  margin-top: 24px;
}
.footer3-section-area .footer-logo-content .logo-content ul {
  margin-top: 24px;
}
.footer3-section-area .footer-logo-content .logo-content ul li {
  display: inline-block;
}
.footer3-section-area .footer-logo-content .logo-content ul li a {
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  display: inline-block;
  transition: all 0.4s;
  background: #221D19;
  border-radius: 50%;
  color: var(--ztc-text-text-1);
  margin: 0 6px 0 0;
}
.footer3-section-area .footer-logo-content .logo-content ul li a:hover {
  background: var(--ztc-bg-bg-7);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
  transform: translateY(-3px);
}
.footer3-section-area .footer-logo-content .service-heading h2 {
  font-family: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-1);
  font-size: var(--ztc-font-size-font-s20);
  text-transform: capitalize;
  margin-bottom: 4px;
}
.footer3-section-area .footer-logo-content .service-heading ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 0.8 !important;
  transition: all 0.4s;
  display: inline-block;
  padding-top: 20px;
}
.footer3-section-area .footer-logo-content .service-heading ul li a:hover {
  color: var(--ztc-bg-bg-7);
  transition: all 0.4s;
  padding-left: 5px;
}
.footer3-section-area .footer-logo-content .service-heading.contact ul li a {
  display: flex;
}
.footer3-section-area .footer-logo-content .service-heading.contact ul li a:hover .icons {
  color: var(--ztc-bg-bg-7);
  transition: all 0.4s;
}
.footer3-section-area .footer-logo-content .service-heading.contact ul li a .icons {
  margin: 0 8px 0 0;
  transition: all 0.4s;
}
.footer3-section-area .copyright p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  line-height: var(--ztc-font-size-font-s24);
  text-align: center;
  padding: 24px 0 30px 0;
}

.footer4-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-text-text-10);
}
.footer4-section-area .footer-logo-content {
  border-bottom: 1px solid #1A1F26;
}
.footer4-section-area .footer-logo-content .logo-content p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  margin-top: 24px;
}
.footer4-section-area .footer-logo-content .logo-content ul {
  margin-top: 24px;
}
.footer4-section-area .footer-logo-content .logo-content ul li {
  display: inline-block;
}
.footer4-section-area .footer-logo-content .logo-content ul li a {
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  display: inline-block;
  transition: all 0.4s;
  background: #1A1E26;
  border-radius: 50%;
  color: var(--ztc-text-text-1);
  margin: 0 6px 0 0;
}
.footer4-section-area .footer-logo-content .logo-content ul li a:hover {
  background: var(--ztc-bg-bg-11);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
  transform: translateY(-3px);
}
.footer4-section-area .footer-logo-content .service-heading h2 {
  font-family: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-1);
  font-size: var(--ztc-font-size-font-s20);
  text-transform: capitalize;
  margin-bottom: 4px;
}
.footer4-section-area .footer-logo-content .service-heading ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  transition: all 0.4s;
  display: inline-block;
  padding-top: 20px;
}
.footer4-section-area .footer-logo-content .service-heading ul li a:hover {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  padding-left: 5px;
}
.footer4-section-area .footer-logo-content .service-heading.contact ul li a {
  display: flex;
}
.footer4-section-area .footer-logo-content .service-heading.contact ul li a:hover .icons {
  background: none;
  transition: all 0.4s;
}
.footer4-section-area .footer-logo-content .service-heading.contact ul li a span.icons {
  height: 30px;
  width: 30px;
  text-align: center;
  line-height: 30px;
  border-radius: 50%;
  background: #01060E;
  transition: all 0.4s;
  display: inline-block;
  margin: 0 8px 0 0;
}
.footer4-section-area .copyright p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  line-height: var(--ztc-font-size-font-s24);
  text-align: center;
  padding: 24px 0 30px 0;
}

/*============= FOOTER CSS AREA ===============*/
/*============= HEADER CSS AREA ===============*/
.homepage1-body .header-area.homepage1 {
  position: absolute;
  width: 100%;
  z-index: 9999;
  padding: 16px 0;
  transition: all 0.4s;
}
.homepage1-body .header-area.homepage1 nav#navbar-example2 {
  display: block !important;
  padding: 0 !important;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li a.nav-link.active {
  color: var(--ztc-text-text-2);
  transition: all 0.4s;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu .tp-submenu {
  left: -370px;
}
.homepage1-body .header-area.homepage1 .header-elements {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 32px;
  position: relative;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  transition: all 0.4s;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu {
  position: relative;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li {
  display: inline-block;
  position: relative;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu {
  background: var(--ztc-text-text-1) !important;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb {
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb:hover .img1::after {
  transform: scale(1);
  transition: all 0.4s;
  visibility: visible;
  opacity: 0.7;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
  top: 38%;
  visibility: visible;
  opacity: 1;
  transition: all 0.6s;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .img1 {
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .img1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-3);
  opacity: 0;
  border-radius: 4px;
  transform: scale(0.8);
  z-index: 1;
  visibility: hidden;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .img1 img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
  transition: all 0.4s;
  border: 1px solid #E5E7EB;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn {
  position: absolute;
  top: 30%;
  z-index: 2;
  visibility: hidden;
  opacity: 0;
  text-align: center;
  transition: all 0.6s;
  margin: 0 auto;
  left: 20%;
  right: 20%;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  background: var(--ztc-text-text-2);
  padding: 16px 20px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1:hover::after {
  width: 100%;
  transition: all 0.4s;
  right: auto;
  left: 0;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 0;
  right: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-1);
  opacity: 0.2;
  z-index: -1;
  border-radius: 4px;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn1 i {
  margin-left: 4px;
  transform: rotate(-45deg);
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu .homemenu-content a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  margin-top: 20px;
  text-align: center;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li:hover .tp-submenu {
  visibility: visible;
  transition: all 0.5s ease-in-out;
  opacity: 1;
  z-index: 9;
  top: 50px;
  position: absolute;
  transition: all 0.4s;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li:hover ul.dropdown-padding {
  visibility: visible;
  transition: all 0.5s ease-in-out;
  opacity: 1;
  z-index: 9;
  top: 50px;
  position: absolute;
  transition: all 0.4s;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  display: inline-block;
  transition: all 0.4s;
  padding: 0 20px;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li:hover > a {
  transition: all 0.4s;
  color: var(--ztc-text-text-2) !important;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li .tp-submenu {
  visibility: hidden;
  opacity: 0;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
  position: absolute;
  background: var(--ztc-text-text-1);
  top: 100px;
  z-index: 1;
  transition: all 0.4s;
  border-radius: 5px;
  padding: 15px;
  left: -300px;
  width: 1300px;
  overflow: hidden;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li ul.dropdown-padding {
  visibility: hidden;
  opacity: 0;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
  position: absolute;
  background: var(--ztc-text-text-1);
  top: 100px;
  width: 225px;
  z-index: 1;
  transition: all 0.4s;
  border-radius: 5px;
  padding: 15px;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li ul.dropdown-padding li.main-small-menu {
  position: relative;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li ul.dropdown-padding li.main-small-menu:hover > a {
  transition: all 0.4s;
  padding-left: 25px;
  color: var(--ztc-text-text-2);
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li ul.dropdown-padding li.main-small-menu:hover > a::after {
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  visibility: visible;
  opacity: 1;
  color: var(--ztc-text-text-2);
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li ul.dropdown-padding li {
  display: block;
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li ul.dropdown-padding li a {
  font-family: var(--ztc-family-font1);
  font-weight: var(--ztc-weight-medium);
  transition: all 0.4s;
  padding: 8px;
  display: block;
  position: relative;
  z-index: 1;
  border-radius: 4px;
  color: var(--ztc-text-text-3);
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li ul.dropdown-padding li a::after {
  position: absolute;
  content: "";
  height: 2px;
  width: 10px;
  transition: all 0.4s;
  z-index: -1;
  left: 10px;
  top: 21px;
  border-radius: 4px;
  display: inline-block;
  visibility: hidden;
  opacity: 0;
  background: var(--ztc-text-text-2);
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li ul.dropdown-padding li a:hover {
  padding-left: 25px;
  color: var(--ztc-text-text-2);
}
.homepage1-body .header-area.homepage1 .header-elements .main-menu ul li ul.dropdown-padding li a:hover::after {
  border-radius: 4px;
  visibility: visible;
  transition: all 0.4s;
  opacity: 1;
}

.header-area.homepage1.sticky {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  transform: translate3d(0, 0, 0);
  z-index: 111;
  animation-name: fade-in-down;
  animation-duration: 1s;
  animation-fill-mode: forwards;
  background: var(--ztc-text-text-3);
  padding: 0;
  transition: all 0.4s;
}
.header-area.homepage1.sticky .header-elements {
  background: none;
  transition: all 0.4s;
  padding: 20px 0;
}

.homepage2-body .header-area.homepage2 {
  position: absolute;
  width: 100%;
  z-index: 9999;
  transition: all 0.4s;
  background: var(--ztc-text-text-1);
}
.homepage2-body .header-area.homepage2 nav#navbar-example2 {
  display: block !important;
  padding: 0 !important;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li a.nav-link.active {
  color: var(--ztc-bg-bg-3);
  transition: all 0.4s;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu .tp-submenu {
  left: -370px;
}
.homepage2-body .header-area.homepage2 .header-elements {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  position: relative;
  border-radius: 4px;
  transition: all 0.4s;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu {
  position: relative;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li {
  display: inline-block;
  position: relative;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu {
  background: var(--ztc-text-text-1) !important;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb {
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb:hover .img1::after {
  transform: scale(1);
  transition: all 0.4s;
  visibility: visible;
  opacity: 0.7;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
  top: 38%;
  visibility: visible;
  opacity: 1;
  transition: all 0.6s;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .img1 {
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .img1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-4);
  opacity: 0;
  border-radius: 4px;
  transform: scale(0.8);
  z-index: 1;
  visibility: hidden;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .img1 img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
  transition: all 0.4s;
  border: 1px solid #E5E7EB;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn {
  position: absolute;
  top: 30%;
  z-index: 2;
  visibility: hidden;
  opacity: 0;
  text-align: center;
  transition: all 0.6s;
  margin: 0 auto;
  left: 20%;
  right: 20%;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-5);
  background: var(--ztc-bg-bg-3);
  padding: 16px 20px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn3:hover {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn3:hover::after {
  left: 0;
  top: 0;
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
  border-radius: 4px;
  width: 100%;
  height: 100%;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn3::after {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  border-radius: 50%;
  left: 45%;
  top: 27%;
  transition: all 0.4s;
  background: var(--ztc-text-text-5);
  z-index: -1;
  visibility: hidden;
  opacity: 0;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn3 i {
  margin-left: 4px;
  transform: rotate(-45deg);
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu .homemenu-content a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-bg-bg-4);
  transition: all 0.4s;
  margin-top: 20px;
  text-align: center;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li:hover .tp-submenu {
  visibility: visible;
  transition: all 0.5s ease-in-out;
  opacity: 1;
  z-index: 9;
  top: 50px;
  position: absolute;
  transition: all 0.4s;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li:hover ul.dropdown-padding {
  visibility: visible;
  transition: all 0.5s ease-in-out;
  opacity: 1;
  z-index: 9;
  top: 50px;
  position: absolute;
  transition: all 0.4s;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
  display: inline-block;
  transition: all 0.4s;
  padding: 0 20px;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li:hover > a {
  transition: all 0.4s;
  color: var(--ztc-bg-bg-3) !important;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li .tp-submenu {
  visibility: hidden;
  opacity: 0;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
  position: absolute;
  background: var(--ztc-bg-bg-4);
  top: 100px;
  z-index: 1;
  transition: all 0.4s;
  border-radius: 5px;
  padding: 15px;
  left: -300px;
  width: 1300px;
  overflow: hidden;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li ul.dropdown-padding {
  visibility: hidden;
  opacity: 0;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
  position: absolute;
  background: var(--ztc-text-text-1);
  top: 100px;
  width: 225px;
  z-index: 1;
  transition: all 0.4s;
  border-radius: 5px;
  padding: 15px;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li ul.dropdown-padding li.main-small-menu {
  position: relative;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li ul.dropdown-padding li.main-small-menu:hover > a {
  transition: all 0.4s;
  padding-left: 25px;
  color: var(--ztc-bg-bg-3);
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li ul.dropdown-padding li.main-small-menu:hover > a::after {
  background: var(--ztc-bg-bg-3);
  transition: all 0.4s;
  visibility: visible;
  opacity: 1;
  color: var(--ztc-bg-bg-3);
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li ul.dropdown-padding li {
  display: block;
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li ul.dropdown-padding li a {
  font-family: var(--ztc-family-font1);
  font-weight: var(--ztc-weight-medium);
  transition: all 0.4s;
  padding: 8px;
  display: block;
  position: relative;
  z-index: 1;
  border-radius: 4px;
  color: var(--ztc-text-text-5);
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li ul.dropdown-padding li a::after {
  position: absolute;
  content: "";
  height: 2px;
  width: 10px;
  transition: all 0.4s;
  z-index: -1;
  left: 10px;
  top: 21px;
  border-radius: 4px;
  display: inline-block;
  visibility: hidden;
  opacity: 0;
  background: var(--ztc-bg-bg-3);
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li ul.dropdown-padding li a:hover {
  padding-left: 25px;
  color: var(--ztc-bg-bg-3);
}
.homepage2-body .header-area.homepage2 .header-elements .main-menu ul li ul.dropdown-padding li a:hover::after {
  border-radius: 4px;
  visibility: visible;
  transition: all 0.4s;
  opacity: 1;
}

.header-area.homepage2.sticky {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  transform: translate3d(0, 0, 0);
  z-index: 111;
  animation-name: fade-in-down;
  animation-duration: 1s;
  animation-fill-mode: forwards;
  background: var(--ztc-text-text-1);
  box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
  padding: 16px 0;
  transition: all 0.4s;
}
.header-area.homepage2.sticky .header-elements {
  background: none;
  transition: all 0.4s;
  padding: 0;
}

.homepage3-body .header-area.homepage3 {
  position: absolute;
  width: 100%;
  z-index: 9999;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-8);
}
.homepage3-body .header-area.homepage3 nav#navbar-example2 {
  display: block !important;
  padding: 0 !important;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li a.nav-link.active {
  color: var(--ztc-text-text-9);
  transition: all 0.4s;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu .tp-submenu {
  left: -370px;
}
.homepage3-body .header-area.homepage3 .header-elements {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  border-radius: 4px;
  transition: all 0.4s;
  padding: 16px 0;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu {
  position: relative;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li {
  display: inline-block;
  position: relative;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu {
  background: var(--ztc-text-text-1) !important;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb {
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb:hover .img1::after {
  transform: scale(1);
  transition: all 0.4s;
  visibility: visible;
  opacity: 0.7;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
  top: 38%;
  visibility: visible;
  opacity: 1;
  transition: all 0.6s;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .img1 {
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .img1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-7);
  opacity: 0;
  border-radius: 4px;
  transform: scale(0.8);
  z-index: 1;
  visibility: hidden;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .img1 img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
  transition: all 0.4s;
  border: 1px solid #E5E7EB;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn {
  position: absolute;
  top: 30%;
  z-index: 2;
  visibility: hidden;
  opacity: 0;
  text-align: center;
  transition: all 0.6s;
  margin: 0 auto;
  left: 20%;
  right: 20%;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  background: var(--ztc-bg-bg-7);
  padding: 16px 20px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn5:hover {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn5:hover::after {
  left: 0;
  top: 0;
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
  border-radius: 4px;
  width: 100%;
  height: 100%;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn5::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 1px;
  border-radius: 4px;
  left: 45%;
  top: 0;
  transition: all 0.4s;
  background: #ff6a00;
  z-index: -1;
  visibility: hidden;
  opacity: 0;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn5 i {
  margin-left: 4px;
  transform: rotate(-45deg);
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu .homemenu-content a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-7);
  transition: all 0.4s;
  margin-top: 20px;
  text-align: center;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li:hover .tp-submenu {
  visibility: visible;
  transition: all 0.5s ease-in-out;
  opacity: 1;
  z-index: 9;
  top: 50px;
  position: absolute;
  transition: all 0.4s;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li:hover ul.dropdown-padding {
  visibility: visible;
  transition: all 0.5s ease-in-out;
  opacity: 1;
  z-index: 9;
  top: 50px;
  position: absolute;
  transition: all 0.4s;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-7);
  display: inline-block;
  transition: all 0.4s;
  padding: 0 20px;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li:hover > a {
  transition: all 0.4s;
  color: var(--ztc-bg-bg-7) !important;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li .tp-submenu {
  visibility: hidden;
  opacity: 0;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
  position: absolute;
  background: var(--ztc-text-text-1);
  top: 100px;
  z-index: 1;
  transition: all 0.4s;
  border-radius: 5px;
  padding: 15px;
  left: -300px;
  width: 1300px;
  overflow: hidden;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li ul.dropdown-padding {
  visibility: hidden;
  opacity: 0;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
  position: absolute;
  background: var(--ztc-text-text-1);
  top: 100px;
  width: 225px;
  z-index: 1;
  transition: all 0.4s;
  border-radius: 5px;
  padding: 15px;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li ul.dropdown-padding li.main-small-menu {
  position: relative;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li ul.dropdown-padding li.main-small-menu:hover > a {
  transition: all 0.4s;
  padding-left: 25px;
  color: var(--ztc-bg-bg-7);
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li ul.dropdown-padding li.main-small-menu:hover > a::after {
  background: var(--ztc-bg-bg-7);
  transition: all 0.4s;
  visibility: visible;
  opacity: 1;
  color: var(--ztc-bg-bg-7);
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li ul.dropdown-padding li {
  display: block;
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li ul.dropdown-padding li a {
  font-family: var(--ztc-family-font1);
  font-weight: var(--ztc-weight-medium);
  transition: all 0.4s;
  padding: 8px;
  display: block;
  position: relative;
  z-index: 1;
  border-radius: 4px;
  color: var(--ztc-text-text-7);
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li ul.dropdown-padding li a::after {
  position: absolute;
  content: "";
  height: 2px;
  width: 10px;
  transition: all 0.4s;
  z-index: -1;
  left: 10px;
  top: 21px;
  border-radius: 4px;
  display: inline-block;
  visibility: hidden;
  opacity: 0;
  background: var(--ztc-bg-bg-7);
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li ul.dropdown-padding li a:hover {
  padding-left: 25px;
  color: var(--ztc-bg-bg-7);
}
.homepage3-body .header-area.homepage3 .header-elements .main-menu ul li ul.dropdown-padding li a:hover::after {
  border-radius: 4px;
  visibility: visible;
  transition: all 0.4s;
  opacity: 1;
}

.header-area.homepage3.sticky {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  transform: translate3d(0, 0, 0);
  z-index: 111;
  animation-name: fade-in-down;
  animation-duration: 1s;
  animation-fill-mode: forwards;
  background: var(--ztc-text-text-1);
  box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
  padding: 0;
  transition: all 0.4s;
}
.header-area.homepage3.sticky .header-elements {
  background: none;
  transition: all 0.4s;
}
.header-area.homepage3.sticky .header-pera {
  display: none;
  transition: all 0.4s;
}

.header-pera {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  padding: 16px 0;
  line-height: var(--ztc-font-size-font-s16);
  background: var(--ztc-text-text-7);
  text-align: center;
  transition: all 0.4s;
}

.homepage4-body .header-area.homepage4 {
  position: absolute;
  width: 100%;
  z-index: 9999;
  padding: 16px 0;
  transition: all 0.4s;
}
.homepage4-body .header-area.homepage4 nav#navbar-example2 {
  display: block !important;
  padding: 0 !important;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li a.nav-link.active {
  color: var(--ztc-bg-bg-11);
  transition: all 0.4s;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu .tp-submenu {
  left: -370px;
}
.homepage4-body .header-area.homepage4 .header-elements {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 32px;
  position: relative;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu {
  position: relative;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li {
  display: inline-block;
  position: relative;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu {
  background: var(--ztc-text-text-1) !important;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb {
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb:hover .img1::after {
  transform: scale(1);
  transition: all 0.4s;
  visibility: visible;
  opacity: 0.7;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb:hover .homemenu-btn {
  top: 38%;
  visibility: visible;
  opacity: 1;
  transition: all 0.6s;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .img1 {
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .img1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-3);
  opacity: 0;
  border-radius: 4px;
  transform: scale(0.8);
  z-index: 1;
  visibility: hidden;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .img1 img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
  transition: all 0.4s;
  border: 1px solid #E5E7EB;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn {
  position: absolute;
  top: 30%;
  z-index: 2;
  visibility: hidden;
  opacity: 0;
  text-align: center;
  transition: all 0.6s;
  margin: 0 auto;
  left: 20%;
  right: 20%;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn7 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  background: var(--ztc-bg-bg-11);
  padding: 16px 20px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn7:hover {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn7:hover::after {
  left: 0;
  top: 0;
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
  border-radius: 4px;
  width: 100%;
  height: 100%;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn7::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 1px;
  border-radius: 4px;
  left: 45%;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-7);
  z-index: -1;
  visibility: hidden;
  opacity: 0;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu .homemenu-thumb .homemenu-btn .header-btn7 i {
  margin-left: 4px;
  transform: rotate(-45deg);
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu .homemenu-content a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  margin-top: 20px;
  text-align: center;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li:hover .tp-submenu {
  visibility: visible;
  transition: all 0.5s ease-in-out;
  opacity: 1;
  z-index: 9;
  top: 50px;
  position: absolute;
  transition: all 0.4s;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li:hover ul.dropdown-padding {
  visibility: visible;
  transition: all 0.5s ease-in-out;
  opacity: 1;
  z-index: 9;
  top: 50px;
  position: absolute;
  transition: all 0.4s;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-10);
  display: inline-block;
  transition: all 0.4s;
  padding: 0 20px;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li:hover > a {
  transition: all 0.4s;
  color: var(--ztc-bg-bg-11) !important;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li .tp-submenu {
  visibility: hidden;
  opacity: 0;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
  position: absolute;
  background: var(--ztc-text-text-1);
  top: 100px;
  z-index: 1;
  transition: all 0.4s;
  border-radius: 5px;
  padding: 15px;
  left: -300px;
  width: 1300px;
  overflow: hidden;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li ul.dropdown-padding {
  visibility: hidden;
  opacity: 0;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
  position: absolute;
  background: var(--ztc-text-text-1);
  top: 100px;
  width: 225px;
  z-index: 1;
  transition: all 0.4s;
  border-radius: 5px;
  padding: 15px;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li ul.dropdown-padding li.main-small-menu {
  position: relative;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li ul.dropdown-padding li.main-small-menu:hover > a {
  transition: all 0.4s;
  padding-left: 25px;
  color: var(--ztc-bg-bg-11);
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li ul.dropdown-padding li.main-small-menu:hover > a::after {
  background: var(--ztc-bg-bg-11);
  transition: all 0.4s;
  visibility: visible;
  opacity: 1;
  color: var(--ztc-bg-bg-11);
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li ul.dropdown-padding li {
  display: block;
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li ul.dropdown-padding li a {
  font-family: var(--ztc-family-font1);
  font-weight: var(--ztc-weight-medium);
  transition: all 0.4s;
  padding: 8px;
  display: block;
  position: relative;
  z-index: 1;
  border-radius: 4px;
  color: var(--ztc-text-text-10);
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li ul.dropdown-padding li a::after {
  position: absolute;
  content: "";
  height: 2px;
  width: 10px;
  transition: all 0.4s;
  z-index: -1;
  left: 10px;
  top: 21px;
  border-radius: 4px;
  display: inline-block;
  visibility: hidden;
  opacity: 0;
  background: var(--ztc-bg-bg-11);
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li ul.dropdown-padding li a:hover {
  padding-left: 25px;
  color: var(--ztc-bg-bg-11);
}
.homepage4-body .header-area.homepage4 .header-elements .main-menu ul li ul.dropdown-padding li a:hover::after {
  border-radius: 4px;
  visibility: visible;
  transition: all 0.4s;
  opacity: 1;
}

.header-area.homepage4.sticky {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  transform: translate3d(0, 0, 0);
  z-index: 111;
  animation-name: fade-in-down;
  animation-duration: 1s;
  animation-fill-mode: forwards;
  background: var(--ztc-text-text-1);
  padding: 0;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 20px 30px;
  transition: all 0.4s;
  padding: 16px 0;
}
.header-area.homepage4.sticky .header-elements {
  background: none;
  transition: all 0.4s;
  padding: 0;
}

/*============= HEADER CSS AREA ENDS ===============*/
/*============= WORK CSS AREA ===============*/
.work1-section-area .tabs-list ul li button {
  background: var(--ztc-bg-bg-1);
  border: none;
  outline: none;
  padding: 14px 16px;
  border-radius: 4px;
  transition: all 0.4s;
  display: flex;
  align-items: center;
  margin-top: 24px;
  text-transform: capitalize;
  width: 380px;
}
@media (max-width: 767px) {
  .work1-section-area .tabs-list ul li button {
    width: 100%;
  }
}
.work1-section-area .tabs-list ul li button .workicons {
  height: 48px;
  width: 48px;
  text-align: center;
  line-height: 48px;
  border-radius: 50%;
  display: inline-block;
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  margin: 0 8px 0 0;
}
.work1-section-area .tabs-list ul li button .worktext {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  display: inline-block;
  line-height: 18px;
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-3);
  text-align: start;
}
@media (max-width: 767px) {
  .work1-section-area .tabs-list ul li button .worktext {
    line-height: 24px;
  }
}
.work1-section-area .tabs-list ul li button.active {
  background: var(--ztc-text-text-2);
  color: var(--ztc-text-text-1);
}
.work1-section-area .tabs-list ul li button.active .workicons {
  background: #D23D48;
}
.work1-section-area .tabs-list ul li button.active .worktext {
  color: var(--ztc-text-text-1);
}
.work1-section-area .works-author-area {
  padding: 0 0 0 70px;
  position: relative;
}
@media (max-width: 767px) {
  .work1-section-area .works-author-area {
    padding: 0;
    margin-top: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .work1-section-area .works-author-area {
    padding: 0;
    margin-top: 30px;
  }
}
.work1-section-area .works-author-area .tab-pane {
  position: relative;
  left: 100px;
  transition: all 0.4s;
}
.work1-section-area .works-author-area .tab-pane .works-side-area {
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
}
.work1-section-area .works-author-area .tab-pane .works-side-area .images img {
  height: 100%;
  width: 100%;
  border-radius: 4px 4px 0 0;
  transition: all 0.4s;
}
.work1-section-area .works-author-area .tab-pane .works-side-area .content-area {
  padding: 32px 50px 32px 32px;
}
@media (max-width: 767px) {
  .work1-section-area .works-author-area .tab-pane .works-side-area .content-area {
    padding: 32px;
  }
}
.work1-section-area .works-author-area .tab-pane .works-side-area .content-area a.power {
  display: inline-block;
  font-size: var(--ztc-font-size-font-s32);
  font-family: var(--ztc-family-font1);
  line-height: var(--ztc-font-size-font-s32);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .work1-section-area .works-author-area .tab-pane .works-side-area .content-area a.power {
    font-size: var(--ztc-font-size-font-s24);
    line-height: var(--ztc-font-size-font-s32);
  }
}
.work1-section-area .works-author-area .tab-pane .works-side-area .content-area a.power:hover {
  color: var(--ztc-text-text-2);
  transition: all 0.4s;
}
.work1-section-area .works-author-area .tab-pane .works-side-area .content-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s26);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
  transition: all 0.4s;
}
.work1-section-area .works-author-area .tab-pane .works-side-area .content-area .btn-area {
  margin-top: 24px;
}
.work1-section-area .works-author-area .tab-pane.fade.show.active {
  left: 0;
  transition: all 0.4s;
}

/*============= WORK CSS AREA ===============*/
/*============= OTHERS CSS AREA STARTS ===============*/
.progress-wrap {
  position: fixed;
  right: 30px;
  bottom: 30px;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  transition: all 200ms linear;
}
.progress-wrap:hover {
  background: var(--ztc-text-text-2);
  transform: translateY(-5px);
  box-shadow: 0 0 15px 0 var(--ztc-text-text-2);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
}

.progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.progress-wrap::after {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  color: var(--ztc-text-text-2);
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 200ms linear;
}

.progress-wrap:hover::after {
  opacity: 0;
}

.progress-wrap::before {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  opacity: 0;
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 2;
  transition: all 200ms linear;
}

.progress-wrap:hover::before {
  opacity: 1;
}

.progress-wrap svg path {
  fill: none;
}

.progress-wrap svg.progress-circle path {
  stroke: var(--ztc-text-text-2);
  stroke-width: 4;
  box-sizing: border-box;
  transition: all 200ms linear;
}

.progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.pagination2 .progress-wrap {
  position: fixed;
  right: 30px;
  bottom: 30px;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  transition: all 200ms linear;
}
.pagination2 .progress-wrap:hover {
  background: var(--ztc-bg-bg-3);
  transform: translateY(-5px);
  box-shadow: 0 0 15px 0 var(--ztc-bg-bg-3);
  transition: all 0.4s;
}
.pagination2 .progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.pagination2 .progress-wrap::after {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  color: var(--ztc-bg-bg-3);
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 200ms linear;
}
.pagination2 .progress-wrap:hover::after {
  opacity: 0;
}
.pagination2 .progress-wrap::before {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  opacity: 0;
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 2;
  transition: all 200ms linear;
}
.pagination2 .progress-wrap:hover::before {
  opacity: 1;
}
.pagination2 .progress-wrap svg path {
  fill: none;
}
.pagination2 .progress-wrap svg.progress-circle path {
  stroke: var(--ztc-bg-bg-3);
  stroke-width: 4;
  box-sizing: border-box;
  transition: all 200ms linear;
}
.pagination2 .progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.pagination3 .progress-wrap {
  position: fixed;
  right: 30px;
  bottom: 30px;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  transition: all 200ms linear;
}
.pagination3 .progress-wrap:hover {
  background: var(--ztc-bg-bg-7);
  transform: translateY(-5px);
  box-shadow: 0 0 15px 0 var(--ztc-bg-bg-7);
  transition: all 0.4s;
}
.pagination3 .progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.pagination3 .progress-wrap::after {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  color: var(--ztc-bg-bg-7);
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 200ms linear;
}
.pagination3 .progress-wrap:hover::after {
  opacity: 0;
}
.pagination3 .progress-wrap::before {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  opacity: 0;
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 2;
  transition: all 200ms linear;
}
.pagination3 .progress-wrap:hover::before {
  opacity: 1;
}
.pagination3 .progress-wrap svg path {
  fill: none;
}
.pagination3 .progress-wrap svg.progress-circle path {
  stroke: var(--ztc-bg-bg-7);
  stroke-width: 4;
  box-sizing: border-box;
  transition: all 200ms linear;
}
.pagination3 .progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.pagination4 .progress-wrap {
  position: fixed;
  right: 30px;
  bottom: 30px;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  transition: all 200ms linear;
}
.pagination4 .progress-wrap:hover {
  background: var(--ztc-bg-bg-11);
  transform: translateY(-5px);
  box-shadow: 0 0 15px 0 var(--ztc-bg-bg-11);
  transition: all 0.4s;
}
.pagination4 .progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.pagination4 .progress-wrap::after {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  color: var(--ztc-bg-bg-11);
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 200ms linear;
}
.pagination4 .progress-wrap:hover::after {
  opacity: 0;
}
.pagination4 .progress-wrap::before {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  opacity: 0;
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 2;
  transition: all 200ms linear;
}
.pagination4 .progress-wrap:hover::before {
  opacity: 1;
}
.pagination4 .progress-wrap svg path {
  fill: none;
}
.pagination4 .progress-wrap svg.progress-circle path {
  stroke: var(--ztc-bg-bg-11);
  stroke-width: 4;
  box-sizing: border-box;
  transition: all 200ms linear;
}
.pagination4 .progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.cursor {
  width: 30px;
  height: 30px;
  border: 1px solid var(--ztc-text-text-2);
  border-radius: 50%;
  position: absolute;
  z-index: 9999;
  transition-duration: 200ms;
  transition-timing-function: ease-out;
  animation: cursorAnim 0.5s alternate;
  pointer-events: none;
}

.cursor::after {
  content: "";
  width: 10px;
  height: 10px;
  position: absolute;
  border: 2px solid var(--ztc-text-text-2);
  border-radius: 50%;
  opacity: 0.5;
  top: 9PX;
  left: 9PX;
  animation: cursorAnim2 0.5s alternate;
  background: var(--ztc-text-text-2);
}

@keyframes cursorAnim {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0.7);
  }
}
@keyframes cursorAnim2 {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0.4);
  }
}
@keyframes cursorAnim3 {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(3);
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}
.expand {
  animation: cursorAnim3 0.5s forwards;
  border: 1px solid var(--ztc-text-text-2);
}

.preloader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999999999;
  background-color: var(--ztc-text-text-1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.preloader2 {
  background: var(--ztc-bg-bg-11) !important;
}

.preloader3 {
  background: var(--ztc-text-text-5) !important;
}

.preloader4 {
  background: var(--ztc-bg-bg-7) !important;
}

.loading-container,
.loading {
  height: 120px;
  position: relative;
  width: 120px;
  border-radius: 100%;
}

.loading-container {
  margin: 40px auto;
}

.loading {
  border: 1px solid transparent;
  border-color: transparent var(--ztc-text-text-2) transparent var(--ztc-text-text-2);
  animation: rotate-loading 1.5s linear 0s infinite normal;
  transform-origin: 50% 50%;
}

.preloader2 .loading {
  border-color: transparent var(--ztc-text-text-1) transparent var(--ztc-text-text-1) !important;
  border: 1px solid transparent;
  animation: rotate-loading 1.5s linear 0s infinite normal;
  transform-origin: 50% 50%;
}

.preloader3 .loading {
  border-color: transparent var(--ztc-bg-bg-3) transparent var(--ztc-bg-bg-3) !important;
  border: 1px solid transparent;
  animation: rotate-loading 1.5s linear 0s infinite normal;
  transform-origin: 50% 50%;
}

.preloader4 .loading {
  border-color: transparent var(--ztc-text-text-1) transparent var(--ztc-text-text-1) !important;
  border: 1px solid transparent;
  animation: rotate-loading 1.5s linear 0s infinite normal;
  transform-origin: 50% 50%;
}

.loading-container:hover .loading,
.loading-container .loading {
  transition: all 0.5s ease-in-out;
}

#loading-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  height: 80px;
  width: 80px;
}

@keyframes rotate-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/*============= OTHERS CSS AREA ENDS ===============*/
/*============= PRICING CSS AREA ===============*/
.pricing-area {
  position: relative;
  z-index: 2;
  overflow-x: hidden;
}

.toggle-inner input {
  position: absolute;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 25px;
  right: 0;
  z-index: 1;
  opacity: 0;
  cursor: pointer;
}

.custom-toggle {
  position: absolute;
  height: 20px;
  width: 20px;
  background-color: var(--ztc-text-text-1);
  top: 5px;
  left: 35px;
  border-radius: 50%;
  transition: 300ms all;
}

.toggle-inner .t-month,
.toggle-inner .t-year {
  position: absolute;
  left: -75px;
  top: 2px;
  transition: 300ms all;
}

.toggle-inner .t-year {
  left: unset;
  left: 73px;
  opacity: 0.5;
}

.active > .toggle-inner .t-month {
  opacity: 0.5;
}

.active > .toggle-inner .t-year {
  opacity: 1;
}

.toggle-inner input:checked + span {
  left: 5px;
}

.toggle-inner {
  width: 60px;
  margin: 0 auto;
  height: 30px;
  border-radius: 25px;
  position: relative;
  background: var(--ztc-text-text-3);
  left: -20px;
}

.t-year h4 {
  min-width: 200px;
}

.t-year {
  text-align: left;
}

.plan-toggle-wrap {
  margin-top: 50px;
  margin-bottom: 32px;
}

.plan-toggle-wrap h4 {
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  font-family: "Figtree", sans-serif;
  margin-bottom: 0;
}

.plan-toggle-wrap h4 span {
  color: var(--ztc-text-text-2);
  font-family: var(--ztc-family-font1);
}

.plan-toggle-wrap1 {
  position: absolute;
  z-index: 1;
  left: 25%;
  top: 30%;
  right: 71%;
}
@media (max-width: 767px) {
  .plan-toggle-wrap1 {
    position: relative;
    left: 0;
    top: 0;
    text-align: center;
    right: 0;
    margin-bottom: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .plan-toggle-wrap1 {
    left: 20%;
    top: 20%;
    right: 0;
  }
}
@media only screen and (min-width: 1700px) and (max-width: 1800px) {
  .plan-toggle-wrap1 {
    right: 56% !important;
  }
}

.plan-toggle-wrap1 h4 {
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  font-family: "Figtree", sans-serif;
  margin-bottom: 0;
}

.plan-toggle-wrap1 h4 span {
  color: var(--ztc-text-text-2);
  font-family: var(--ztc-family-font1);
}

.single-pricing-area {
  margin-bottom: 30px;
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  padding: 30px;
  border: 1px solid var(--ztc-bg-bg-1);
  transition: all 0.4s;
}
.single-pricing-area:hover {
  border: 1px solid var(--ztc-text-text-2);
  transition: all 0.4s;
}
.single-pricing-area .pricing-box h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  line-height: var(--ztc-font-size-font-s18);
  margin-bottom: 20px;
}
.single-pricing-area .pricing-box p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-text-text-4);
  line-height: var(--ztc-font-size-font-s26);
  font: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-4);
  font-weight: var(--ztc-weight-medium);
}
.single-pricing-area .pricing-box h2 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s44);
  line-height: var(--ztc-font-size-font-s44);
  color: var(--ztc-text-text-3);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 4px;
  margin-top: 24px;
}
.single-pricing-area .pricing-box .header-btn1 {
  width: 100%;
  margin-top: 24px;
  text-align: center;
}
.single-pricing-area .pricing-box h4 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s20);
  line-height: var(--ztc-font-size-font-s20);
  color: var(--ztc-text-text-3);
  margin-top: 24px;
  margin-bottom: 24px;
  font-weight: var(--ztc-weight-bold);
}
.single-pricing-area .pricing-box ul li {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-4);
  font-weight: var(--ztc-weight-medium);
  margin-top: 16px;
}
.single-pricing-area .pricing-box ul li img {
  margin: 0 8px 0 0;
}

.single-pricing-area.active {
  border: 1px solid var(--ztc-text-text-2);
  transition: all 0.4s;
}

.compareplan-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-bg-bg-1);
}
.compareplan-section-area .single-pricing-area {
  background: var(--ztc-text-text-1);
  padding: 32px 40px !important;
  position: relative;
  margin-bottom: 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .compareplan-section-area .single-pricing-area {
    padding: 20px !important;
  }
}
.compareplan-section-area .comparison table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
}
.compareplan-section-area .comparison td, .compareplan-section-area .comparison th {
  text-align: center;
}
.compareplan-section-area .comparison tbody tr:nth-child(odd) {
  display: none;
}
.compareplan-section-area .comparison .compare-row td {
  padding: 25px 0;
  border-top: 1px solid #FAE7E8;
}
.compareplan-section-area .comparison tr td:first-child {
  text-align: left;
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  padding: 25px 0;
  border-top: 1px solid #FAE7E8;
}
.compareplan-section-area .comparison .tl2 {
  font-family: var(--ztc-family-font1);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  font-size: var(--ztc-font-size-font-s24);
}
.compareplan-section-area .comparison .tl2 span {
  display: inline-block;
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
  font-family: var(--ztc-family-font1);
  margin-top: 32px;
  position: absolute;
  left: 6%;
  top: 55px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .compareplan-section-area .comparison .tl2 span {
    display: none;
  }
}
.compareplan-section-area .comparison .qbo {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  line-height: var(--ztc-font-size-font-s20);
  padding: 0 !important;
}
.compareplan-section-area th.price-info.hide-mobile {
  padding-bottom: 40px;
}
.compareplan-section-area .comparison .price-now span {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s44);
  line-height: var(--ztc-font-size-font-s44);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-3);
  transition: all 0.4s;
  display: inline-block;
  margin-top: 20px;
  margin-bottom: 24px;
}
@media (max-width: 767px) {
  .compareplan-section-area .comparison {
    font-family: var(--ztc-family-font1);
    font-size: var(--ztc-font-size-font-s20);
    font-weight: var(--ztc-weight-bold);
    color: var(--ztc-text-text-3);
    transition: all 0.4s;
    padding: 20px !important;
    background: var(--ztc-text-text-1);
  }
  .compareplan-section-area th.price-info.hide-mobile {
    padding-bottom: 20px;
  }
  .compareplan-section-area .comparison .qbo {
    background: none;
    padding: 10px !important;
  }
  .compareplan-section-area .comparison td:first-child, .compareplan-section-area .comparison th:first-child {
    display: none;
  }
  .compareplan-section-area .comparison tbody tr:nth-child(odd) {
    display: table-row;
  }
  .compareplan-section-area .comparison .row {
    background: #fff;
  }
  .compareplan-section-area .comparison td, .compareplan-section-area .comparison th {
    border: 1px solid #FAE7E8;
    padding: 20px 0;
  }
}

/*============= PRICING CSS AREA ENDS ===============*/
/*============= SLIDER CSS AREA ===============*/
.slider-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-bg-bg-1);
  padding: 80px 0;
}
@media (max-width: 767px) {
  .slider-section-area {
    padding: 40px 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .slider-section-area {
    padding: 40px 0;
  }
}
.slider-section-area .header-slider {
  margin-bottom: 60px;
}
.slider-section-area .header-slider h3 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s20);
  line-height: var(--ztc-font-size-font-s20);
  color: var(--ztc-text-text-3);
  font-weight: var(--ztc-weight-bold);
  text-transform: capitalize;
}
.slider-section-area .slider-images .img1 img {
  height: 40px;
  width: 160px;
  -o-object-fit: contain;
     object-fit: contain;
}

/*============= SLIDER CSS AREA ENDS ===============*/
/*============= MISSION CSS AREA ===============*/
.mission-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-bg-bg-1);
}
.mission-section-area .mission-header-area {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .mission-section-area .mission-header-area {
    margin-bottom: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .mission-section-area .mission-header-area {
    margin-bottom: 30px;
  }
}
.mission-section-area .tabs-auhtor-area .nav.nav-pills {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40px;
}
.mission-section-area .tabs-auhtor-area .nav.nav-pills .nav-item {
  display: inline-block;
}
.mission-section-area .tabs-auhtor-area .nav.nav-pills .nav-item button {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-10);
  font-weight: var(--ztc-weight-medium) !important;
  transition: all 0.4s;
  background: var(--ztc-text-text-1);
  display: inline-block;
  width: 400px;
  text-align: center;
  padding: 20px !important;
  border-radius: 4px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .mission-section-area .tabs-auhtor-area .nav.nav-pills .nav-item button {
    width: 215px;
  }
}
@media (max-width: 767px) {
  .mission-section-area .tabs-auhtor-area .nav.nav-pills .nav-item button {
    width: 100%;
    margin-bottom: 10px;
  }
}
.mission-section-area .tabs-auhtor-area .nav.nav-pills .nav-link.active {
  background: var(--ztc-bg-bg-11);
  color: var(--ztc-text-text-1);
}
.mission-section-area .tabs-auhtor-area .nav.nav-pills .nav-link {
  padding: 0 !important;
}
.mission-section-area .tabs-auhtor-area .tab-pane .mission-img img {
  height: 100%;
  width: 100%;
  transition: all 0.4s;
  border-radius: 4px;
  -o-object-fit: cover;
     object-fit: cover;
}
.mission-section-area .tabs-auhtor-area .tab-pane .mission-content-area {
  padding: 0 0 0 75px;
}
@media (max-width: 767px) {
  .mission-section-area .tabs-auhtor-area .tab-pane .mission-content-area {
    padding: 0;
    margin-top: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .mission-section-area .tabs-auhtor-area .tab-pane .mission-content-area {
    padding: 0;
    margin-top: 30px;
  }
}
.mission-section-area .tabs-auhtor-area .tab-pane .mission-content-area p {
  margin-bottom: 16px;
}
.mission-section-area .tabs-auhtor-area .tab-pane .mission-content-area .btn-area {
  margin-top: 32px;
}
.mission-section-area .tabs-auhtor-area .tab-pane.fade {
  top: 120px;
  position: relative;
  transition: auto;
  transition: all 0.6s;
  transform: rotateX(50px);
}
.mission-section-area .tabs-auhtor-area .tab-pane.fade.show {
  top: 0;
  transition: all 0.6s;
  transform: rotateX(0);
}

/*============= MISSION CSS AREA ENDS ===============*/
/*============= TEAM CSS AREA ===============*/
.team1-section-area .team-header-area {
  margin-bottom: 50px;
}
.team1-section-area .team-auhtor-boxarea {
  position: relative;
  z-index: 1;
  overflow: hidden;
  margin-bottom: 30px;
}
.team1-section-area .team-auhtor-boxarea:hover .images {
  transition: all 0.4s;
}
.team1-section-area .team-auhtor-boxarea:hover .images::after {
  height: 100%;
  width: 100%;
  transition: all 0.4s;
}
.team1-section-area .team-auhtor-boxarea:hover .images img {
  transform: scale(1.1) rotate(-4deg);
  transition: all 0.4s;
}
.team1-section-area .team-auhtor-boxarea .team-social-area .icons a.plus {
  height: 48px;
  width: 48px;
  text-align: center;
  line-height: 53px;
  display: inline-block;
  border-radius: 50%;
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 2;
}
.team1-section-area .team-auhtor-boxarea .team-social-area .icons a.plus i {
  font-size: var(--ztc-font-size-font-s20);
  transition: all 0.4s;
}
.team1-section-area .team-auhtor-boxarea .team-social-area .icons:hover .plus i {
  transform: rotate(-45deg);
  transition: all 0.4s;
}
.team1-section-area .team-auhtor-boxarea .team-social-area .icons:hover ul {
  right: 20px;
  transition: all 0.6s;
}
.team1-section-area .team-auhtor-boxarea .team-social-area ul {
  position: absolute;
  top: 28%;
  right: -50px;
  transition: all 0.6s;
  z-index: 2;
}
.team1-section-area .team-auhtor-boxarea .team-social-area ul li a {
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  background: var(--ztc-text-text-1);
  transition: all 0.4s;
  display: inline-block;
  color: var(--ztc-text-text-3);
  margin: 0 0 8px 0;
}
.team1-section-area .team-auhtor-boxarea .team-social-area ul li a:hover {
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
}
.team1-section-area .team-auhtor-boxarea .images {
  position: relative;
  z-index: 1;
  overflow: hidden;
  border-radius: 4px;
}
.team1-section-area .team-auhtor-boxarea .images::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-2);
  opacity: 0.5;
}
.team1-section-area .team-auhtor-boxarea .images img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
  transition: all 0.4s;
}
.team1-section-area .team-auhtor-boxarea .content-area {
  margin-top: 24px;
}
.team1-section-area .team-auhtor-boxarea .content-area a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  display: inline-block;
  transition: all 0.4s;
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-3);
  font-weight: var(--ztc-weight-bold);
}
.team1-section-area .team-auhtor-boxarea .content-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  color: var(--ztc-text-text-4);
  font-weight: var(--ztc-weight-medium);
  margin-top: 16px;
}
.team1-section-area .pagination-area ul {
  text-align: center;
  justify-content: center;
  margin-top: 30px;
}
.team1-section-area .pagination-area ul li a {
  height: 50px;
  width: 50px;
  display: inline-block;
  border-radius: 4px !important;
  transition: all 0.4s;
  border: none;
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s40);
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-3);
  margin: 0 16px;
  box-shadow: none;
  background: var(--ztc-bg-bg-1);
}
.team1-section-area .pagination-area ul li a:hover {
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
}
.team1-section-area .pagination-area ul li .page-link.active {
  background: var(--ztc-text-text-2) !important;
  color: var(--ztc-text-text-1);
}

.team2-section-area {
  position: relative;
  z-index: 1;
}
.team2-section-area .team-header {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .team2-section-area .team-header {
    margin-bottom: 30px;
  }
}
.team2-section-area .team-author-boxarea {
  position: relative;
  overflow: hidden;
  margin-bottom: 30px;
}
.team2-section-area .team-author-boxarea:hover .content {
  left: 0;
  transition: all 0.6s;
}
.team2-section-area .team-author-boxarea:hover .images::after {
  height: 100%;
  transition: all 0.4s;
}
.team2-section-area .team-author-boxarea:hover .images img {
  transform: scale(1.1);
  transition: all 0.6s;
}
.team2-section-area .team-author-boxarea .images {
  overflow: hidden;
  transition: all 0.4s;
  border-radius: 4px;
  position: relative;
  z-index: 1;
}
.team2-section-area .team-author-boxarea .images::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-5);
  opacity: 0.5;
}
.team2-section-area .team-author-boxarea .images img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
  transition: all 0.6s;
}
.team2-section-area .team-author-boxarea .content {
  background: var(--ztc-text-text-1);
  border-radius: 0 4px 4px 0;
  transition: all 0.6s;
  position: absolute;
  bottom: 50px;
  left: -200px;
  z-index: 2;
  padding: 24px;
}
.team2-section-area .team-author-boxarea .content a {
  font-size: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-5);
  transition: all 0.4s;
  font-family: var(--ztc-family-font1);
  display: inline-block;
  line-height: var(--ztc-font-size-font-s20);
}
.team2-section-area .team-author-boxarea .content p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-6);
  margin-top: 10px;
  transition: all 0.4s;
}
.team2-section-area .team-author-boxarea .share-area:hover .icons a {
  color: var(--ztc-text-text-5);
  transition: all 0.4s;
  background: var(--ztc-bg-bg-3);
}
.team2-section-area .team-author-boxarea .share-area:hover .list ul {
  right: 10px;
  transition: all 0.6s;
}
.team2-section-area .team-author-boxarea .share-area .icons a {
  font-size: var(--ztc-font-size-font-s32);
  height: 70px;
  width: 70px;
  text-align: center;
  line-height: 70px;
  background: var(--ztc-text-text-5);
  color: var(--ztc-text-text-1);
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
  border-radius: 0 4px 0 100px;
  display: inline-block;
  transition: all 0.4s;
}
.team2-section-area .team-author-boxarea .share-area .icons a i {
  position: absolute;
  top: 15px;
  left: 25px;
}
.team2-section-area .team-author-boxarea .share-area .list ul {
  position: absolute;
  top: 80px;
  right: -100px;
  z-index: 2;
  transition: all 0.6s;
}
.team2-section-area .team-author-boxarea .share-area .list ul li a {
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
  background: var(--ztc-text-text-5);
  display: inline-block;
  margin-bottom: 8px;
}
.team2-section-area .team-author-boxarea .share-area .list ul li a:hover {
  background: var(--ztc-bg-bg-3);
  transition: all 0.4s;
  color: var(--ztc-text-text-5);
}

.team3-section-area .team-header-area {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .team3-section-area .team-header-area {
    margin-bottom: 30px;
  }
}
.team3-section-area .team-auhtor-boxes {
  position: relative;
  z-index: 1;
  border-radius: 4px;
  transition: all 0.4s;
  margin-bottom: 30px;
}
.team3-section-area .team-auhtor-boxes:hover .img1::after {
  height: 100%;
  transition: all 0.6s;
}
.team3-section-area .team-auhtor-boxes:hover .img1 img {
  transform: scale(1.1);
  transition: all 0.4s;
}
.team3-section-area .team-auhtor-boxes:hover .content-area {
  height: 166px;
  transition: all 0.6s;
}
.team3-section-area .team-auhtor-boxes:hover .content-area ul {
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
  height: 66px;
}
.team3-section-area .team-auhtor-boxes .img1 {
  position: relative;
  z-index: 1;
  overflow: hidden;
  border-radius: 4px;
  transition: all 0.4s;
}
.team3-section-area .team-auhtor-boxes .img1::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  left: 0;
  top: 0;
  background: var(--ztc-bg-bg-7);
  opacity: 0.7;
  transition: all 0.6s;
}
.team3-section-area .team-auhtor-boxes .img1 img {
  height: 100%;
  width: 100%;
  transition: all 0.4s;
}
.team3-section-area .team-auhtor-boxes .content-area {
  text-align: center;
  background: var(--ztc-text-text-1);
  border-radius: 4px;
  transition: all 0.6s;
  overflow: hidden;
  padding: 24px;
  position: absolute;
  z-index: 1;
  bottom: 24px;
  right: 24px;
  left: 24px;
  height: 102px;
}
.team3-section-area .team-auhtor-boxes .content-area a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s20);
  line-height: var(--ztc-font-size-font-s20);
  color: var(--ztc-text-text-7);
  font-weight: var(--ztc-weight-bold);
  transition: all 0.4s;
  display: inline-block;
  margin-bottom: 16px;
}
.team3-section-area .team-auhtor-boxes .content-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  color: var(--ztc-text-text-8);
  font-weight: var(--ztc-weight-medium);
  transition: all 0.4s;
}
.team3-section-area .team-auhtor-boxes .content-area ul {
  margin-top: 24px;
  visibility: hidden;
  opacity: 1;
  transition: all 0.4s;
  height: 0;
}
.team3-section-area .team-auhtor-boxes .content-area ul li {
  display: inline-block;
}
.team3-section-area .team-auhtor-boxes .content-area ul li a {
  display: inline-block;
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  transition: all 0.4s;
  color: var(--ztc-bg-bg-7);
  background: #FDF0E5;
  margin: 0 8px 0 0;
}
.team3-section-area .team-auhtor-boxes .content-area ul li a:hover {
  background: var(--ztc-bg-bg-7);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: translateY(-5px);
}

.team4-section-area .team-header-area {
  margin-bottom: 50px;
}
.team4-section-area .team-auhtor-boxarea {
  position: relative;
  z-index: 1;
  overflow: hidden;
  margin-bottom: 30px;
  background: var(--ztc-bg-bg-1);
  border-radius: 4px;
  transition: all 0.4s;
}
.team4-section-area .team-auhtor-boxarea:hover {
  background: var(--ztc-bg-bg-11);
  transition: all 0.4s;
}
.team4-section-area .team-auhtor-boxarea:hover .images {
  transition: all 0.4s;
}
.team4-section-area .team-auhtor-boxarea:hover .images::after {
  height: 100%;
  width: 100%;
  transition: all 0.4s;
}
.team4-section-area .team-auhtor-boxarea:hover .images img {
  transform: scale(1.1) rotate(-4deg);
  transition: all 0.4s;
}
.team4-section-area .team-auhtor-boxarea:hover .content-area a {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.team4-section-area .team-auhtor-boxarea:hover .content-area p {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.team4-section-area .team-auhtor-boxarea:hover .icons ul {
  transition: all 0.6s;
  bottom: 20px;
}
.team4-section-area .team-auhtor-boxarea .team-social-area {
  text-align: center;
}
.team4-section-area .team-auhtor-boxarea .team-social-area ul {
  position: absolute;
  bottom: -100px;
  transition: all 0.6s;
  z-index: 2;
  text-align: center;
  left: 25%;
  right: 25%;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .team4-section-area .team-auhtor-boxarea .team-social-area ul {
    left: 22%;
    right: 22%;
  }
}
@media (max-width: 767px) {
  .team4-section-area .team-auhtor-boxarea .team-social-area ul {
    left: 20%;
    right: 20%;
  }
}
.team4-section-area .team-auhtor-boxarea .team-social-area ul li {
  display: inline-block;
}
.team4-section-area .team-auhtor-boxarea .team-social-area ul li a {
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  background: var(--ztc-text-text-1);
  transition: all 0.4s;
  display: inline-block;
  color: var(--ztc-bg-bg-11);
  margin: 0 0 8px 0;
}
.team4-section-area .team-auhtor-boxarea .team-social-area ul li a:hover {
  background: var(--ztc-bg-bg-11);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
}
.team4-section-area .team-auhtor-boxarea .images {
  position: relative;
  z-index: 1;
  overflow: hidden;
  border-radius: 4px 4px 0 0;
}
.team4-section-area .team-auhtor-boxarea .images::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-11);
  opacity: 0.7;
}
.team4-section-area .team-auhtor-boxarea .images img {
  height: 100%;
  width: 100%;
  border-radius: 4px 4px 0 0;
  transition: all 0.4s;
}
.team4-section-area .team-auhtor-boxarea .content-area {
  padding: 24px;
  text-align: center;
}
.team4-section-area .team-auhtor-boxarea .content-area a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  display: inline-block;
  transition: all 0.4s;
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-10);
  font-weight: var(--ztc-weight-bold);
}
.team4-section-area .team-auhtor-boxarea .content-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  color: var(--ztc-text-text-11);
  font-weight: var(--ztc-weight-medium);
  margin-top: 16px;
  transition: all 0.4s;
}

/*============= TEAM CSS AREA STARTS ===============*/
/*============= FAQ CSS AREA ===============*/
.faq1-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-bg-bg-1);
}
.faq1-section-area .faq-header-area .btn-area {
  margin-top: 32px;
}
@media (max-width: 767px) {
  .faq1-section-area .faq-auhtoir-area1 {
    margin-top: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .faq1-section-area .faq-auhtoir-area1 {
    margin-top: 30px;
  }
}
.faq1-section-area .faq-auhtoir-area1 .accordion .accordion-item {
  border: none;
  background: var(--ztc-text-text-2);
  border-radius: 6px;
}
.faq1-section-area .faq-auhtoir-area1 .accordion .accordion-item .accordion-button::after {
  filter: brightness(0);
  position: absolute;
  right: 16px;
}
.faq1-section-area .faq-auhtoir-area1 .accordion .accordion-item .accordion-header .accordion-button:not(.collapsed) {
  background: none;
  color: var(--ztc-text-text-1);
}
.faq1-section-area .faq-auhtoir-area1 .accordion .accordion-item .accordion-header .accordion-button:not(.collapsed)::after {
  filter: brightness(0) invert(1);
}
.faq1-section-area .faq-auhtoir-area1 .accordion .accordion-item .accordion-header button {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  color: var(--ztc-text-text-3);
  display: inline-block;
  font-weight: var(--ztc-weight-bold);
  text-transform: capitalize;
  border: none;
  box-shadow: none;
  padding: 20px 16px 20px 16px;
}
@media (max-width: 767px) {
  .faq1-section-area .faq-auhtoir-area1 .accordion .accordion-item .accordion-header button {
    line-height: var(--ztc-font-size-font-s26);
  }
}
.faq1-section-area .faq-auhtoir-area1 .accordion .accordion-item .accordion-body {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  line-height: var(--ztc-font-size-font-s26);
  padding: 0 16px 16px 16px;
}

.faq2-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-bg-bg-1);
}
.faq2-section-area .faq-header-area {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .faq2-section-area .faq-header-area {
    margin-bottom: 30px;
  }
}
.faq2-section-area .faq-images img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
}
@media (max-width: 767px) {
  .faq2-section-area .faq-auhtor-area1 {
    margin-top: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .faq2-section-area .faq-auhtor-area1 {
    margin-top: 30px;
  }
}
.faq2-section-area .faq-auhtor-area1 .accordion .accordion-item {
  border: none;
  border-radius: 6px;
  background: var(--ztc-text-text-5);
}
.faq2-section-area .faq-auhtor-area1 .accordion .accordion-item .accordion-button::after {
  filter: brightness(0);
  position: absolute;
  right: 16px;
}
.faq2-section-area .faq-auhtor-area1 .accordion .accordion-item .accordion-header .accordion-button:not(.collapsed) {
  background: none;
  color: var(--ztc-text-text-1);
}
.faq2-section-area .faq-auhtor-area1 .accordion .accordion-item .accordion-header .accordion-button:not(.collapsed)::after {
  filter: brightness(0) invert(1);
}
.faq2-section-area .faq-auhtor-area1 .accordion .accordion-item .accordion-header button {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  color: var(--ztc-text-text-5);
  display: inline-block;
  font-weight: var(--ztc-weight-bold);
  text-transform: capitalize;
  border: none;
  box-shadow: none;
  padding: 20px 16px 20px 16px;
}
@media (max-width: 767px) {
  .faq2-section-area .faq-auhtor-area1 .accordion .accordion-item .accordion-header button {
    line-height: var(--ztc-font-size-font-s26);
  }
}
.faq2-section-area .faq-auhtor-area1 .accordion .accordion-item .accordion-body {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  line-height: var(--ztc-font-size-font-s26);
  padding: 0 16px 16px 16px;
}

.faq4-section-area {
  position: relative;
  z-index: 1;
}
.faq4-section-area .faq-header-area {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .faq4-section-area .faq-header-area {
    margin-bottom: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .faq4-section-area .faq-header-area {
    margin-bottom: 30px;
  }
}
.faq4-section-area .faq-images img {
  height: 100%;
  width: 100%;
  border-radius: 4px;
  transition: all 0.4s;
}
.faq4-section-area .faq-auhtoir-area2 {
  padding: 0 0 0 50px;
}
@media (max-width: 767px) {
  .faq4-section-area .faq-auhtoir-area2 {
    margin-top: 30px;
    padding: 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .faq4-section-area .faq-auhtoir-area2 {
    margin-top: 30px;
    padding: 0;
  }
}
.faq4-section-area .faq-auhtoir-area2 .accordion .accordion-item {
  border: none;
  background: var(--ztc-bg-bg-11);
  border-radius: 6px;
}
.faq4-section-area .faq-auhtoir-area2 .accordion .accordion-item .accordion-button::after {
  filter: brightness(0);
  position: absolute;
  right: 16px;
}
.faq4-section-area .faq-auhtoir-area2 .accordion .accordion-item .accordion-header .accordion-button:not(.collapsed) {
  background: none;
  color: var(--ztc-text-text-1);
}
.faq4-section-area .faq-auhtoir-area2 .accordion .accordion-item .accordion-header .accordion-button:not(.collapsed)::after {
  filter: brightness(0) invert(1);
}
.faq4-section-area .faq-auhtoir-area2 .accordion .accordion-item .accordion-header button {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s18);
  color: var(--ztc-text-text-10);
  display: inline-block;
  font-weight: var(--ztc-weight-bold);
  text-transform: capitalize;
  border: none;
  box-shadow: none;
  padding: 20px 16px 20px 16px;
  background: var(--ztc-bg-bg-1);
}
@media (max-width: 767px) {
  .faq4-section-area .faq-auhtoir-area2 .accordion .accordion-item .accordion-header button {
    line-height: var(--ztc-font-size-font-s26);
  }
}
.faq4-section-area .faq-auhtoir-area2 .accordion .accordion-item .accordion-body {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  line-height: var(--ztc-font-size-font-s26);
  padding: 0 16px 16px 16px;
}

.faq1-section-area.faq-inner {
  background: var(--ztc-text-text-1) !important;
}
.faq1-section-area.faq-inner .faq-auhtoir-area1 button {
  background: var(--ztc-bg-bg-1);
}

/*============= FAQ CSS AREA ===============*/
/*============= CONTACT CSS AREA ===============*/
.contact1-section-area {
  position: relative;
  z-index: 1;
}
.contact1-section-area::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  background: var(--ztc-text-text-3);
  opacity: 0.7;
  z-index: -1;
}
.contact1-section-area .contact-header-area {
  padding: 0 70px 0 0;
}
@media (max-width: 767px) {
  .contact1-section-area .contact-header-area {
    padding: 0;
    margin-bottom: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .contact1-section-area .contact-header-area {
    padding: 0;
    margin-bottom: 30px;
  }
}
.contact1-section-area .contact-header-area h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  border-radius: 4px;
  padding: 8px 12px;
  text-transform: capitalize;
  display: inline-block;
  position: relative;
  z-index: 1;
  margin-bottom: 20px;
}
.contact1-section-area .contact-header-area h5::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  opacity: 10%;
  z-index: -1;
}
.contact1-section-area .contact-header-area h2 {
  color: var(--ztc-text-text-1);
}
.contact1-section-area .contact-header-area p {
  color: var(--ztc-text-text-1);
  opacity: 80%;
}
.contact1-section-area .contact-header-area .contact-auhtor-side .icons-text {
  display: flex;
  align-items: center;
}
.contact1-section-area .contact-header-area .contact-auhtor-side .icons-text .icons {
  height: 48px;
  width: 48px;
  text-align: center;
  line-height: 48px;
  border-radius: 4px;
  display: inline-block;
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  background: var(--ztc-text-text-2);
}
.contact1-section-area .contact-header-area .contact-auhtor-side .icons-text .text {
  padding-left: 16px;
}
.contact1-section-area .contact-header-area .contact-auhtor-side .icons-text .text p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  line-height: 20px;
  color: var(--ztc-text-text-1);
}
.contact1-section-area .contact-header-area .contact-auhtor-side .icons-text .text a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-1);
  display: inline-block;
  transition: all 0.4s;
}
.contact1-section-area .contact-header-area .contact-auhtor-side .icons-text .text a:hover {
  color: var(--ztc-text-text-2);
  transition: all 0.4s;
}
.contact1-section-area .contact-submit-boxarea {
  padding: 32px 40px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}
.contact1-section-area .contact-submit-boxarea h4 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s32);
  line-height: var(--ztc-font-size-font-s32);
  color: var(--ztc-text-text-1);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 12px;
  text-transform: capitalize;
}
.contact1-section-area .contact-submit-boxarea .input-area {
  margin-top: 20px;
}
.contact1-section-area .contact-submit-boxarea .input-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  margin-bottom: 12px;
}
.contact1-section-area .contact-submit-boxarea .input-area input {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-3);
  width: 100%;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  padding: 16px;
  height: 48px;
}
.contact1-section-area .contact-submit-boxarea .input-area input::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
  opacity: 50%;
}
.contact1-section-area .contact-submit-boxarea .input-area input::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
  opacity: 50%;
}
.contact1-section-area .contact-submit-boxarea .input-area textarea {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-3);
  width: 100%;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  padding: 16px;
  height: 150px;
}
.contact1-section-area .contact-submit-boxarea .input-area textarea::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
  opacity: 50%;
}
.contact1-section-area .contact-submit-boxarea .input-area textarea::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
  opacity: 50%;
}
.contact1-section-area .contact-submit-boxarea .input-area1 {
  margin-top: 20px;
  display: flex;
}
.contact1-section-area .contact-submit-boxarea .input-area1 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  padding-left: 8px;
}
.contact1-section-area .contact-submit-boxarea .input-area1 input[type=checkbox] {
  height: 16px;
  width: 16px;
  border: 1px solid #9EA4A9;
  border-radius: 5px;
  background: none;
  accent-color: var(--ztc-text-text-2);
}
.contact1-section-area .contact-submit-boxarea .input-area1 button {
  border: none;
  outline: none;
  margin-top: 12px;
}

.contact2-section-area {
  position: relative;
  z-index: 1;
}
.contact2-section-area::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  background: var(--ztc-text-text-5);
  opacity: 0.8;
  z-index: -1;
}
.contact2-section-area .contact-header-area {
  padding: 0 70px 0 0;
}
@media (max-width: 767px) {
  .contact2-section-area .contact-header-area {
    padding: 0;
    margin-bottom: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .contact2-section-area .contact-header-area {
    padding: 0;
    margin-bottom: 30px;
  }
}
.contact2-section-area .contact-header-area h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  border-radius: 4px;
  padding: 8px 12px;
  text-transform: capitalize;
  display: inline-block;
  position: relative;
  z-index: 1;
  margin-bottom: 20px;
}
.contact2-section-area .contact-header-area h5::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  opacity: 10%;
  z-index: -1;
}
.contact2-section-area .contact-header-area h2 {
  color: var(--ztc-text-text-1);
}
.contact2-section-area .contact-header-area p {
  color: var(--ztc-text-text-1);
  opacity: 0.8;
}
.contact2-section-area .contact-header-area .contact-auhtor-side .icons-text {
  display: flex;
  align-items: center;
}
.contact2-section-area .contact-header-area .contact-auhtor-side .icons-text .icons {
  height: 48px;
  width: 48px;
  text-align: center;
  line-height: 48px;
  border-radius: 4px;
  display: inline-block;
  color: var(--ztc-text-text-5);
  transition: all 0.4s;
  background: var(--ztc-bg-bg-3);
}
.contact2-section-area .contact-header-area .contact-auhtor-side .icons-text .text {
  padding-left: 16px;
}
.contact2-section-area .contact-header-area .contact-auhtor-side .icons-text .text p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  line-height: 20px;
  color: var(--ztc-text-text-1);
}
.contact2-section-area .contact-header-area .contact-auhtor-side .icons-text .text a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-1);
  display: inline-block;
  transition: all 0.4s;
}
.contact2-section-area .contact-header-area .contact-auhtor-side .icons-text .text a:hover {
  color: var(--ztc-bg-bg-3);
  transition: all 0.4s;
}
.contact2-section-area .contact-submit-boxarea {
  padding: 32px 40px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}
.contact2-section-area .contact-submit-boxarea h4 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s32);
  line-height: var(--ztc-font-size-font-s32);
  color: var(--ztc-text-text-1);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 12px;
  text-transform: capitalize;
}
.contact2-section-area .contact-submit-boxarea .input-area {
  margin-top: 20px;
}
.contact2-section-area .contact-submit-boxarea .input-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  margin-bottom: 12px;
}
.contact2-section-area .contact-submit-boxarea .input-area input {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-3);
  width: 100%;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  padding: 16px;
  height: 48px;
}
.contact2-section-area .contact-submit-boxarea .input-area input::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
  opacity: 50%;
}
.contact2-section-area .contact-submit-boxarea .input-area input::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
  opacity: 50%;
}
.contact2-section-area .contact-submit-boxarea .input-area textarea {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-3);
  width: 100%;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  padding: 16px;
  height: 150px;
}
.contact2-section-area .contact-submit-boxarea .input-area textarea::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
  opacity: 50%;
}
.contact2-section-area .contact-submit-boxarea .input-area textarea::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
  opacity: 50%;
}
.contact2-section-area .contact-submit-boxarea .input-area1 {
  margin-top: 20px;
  display: flex;
}
.contact2-section-area .contact-submit-boxarea .input-area1 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  padding-left: 8px;
}
.contact2-section-area .contact-submit-boxarea .input-area1 input[type=checkbox] {
  height: 16px;
  width: 16px;
  border: 1px solid #9EA4A9;
  border-radius: 5px;
  background: none;
  accent-color: var(--ztc-bg-bg-3);
}
.contact2-section-area .contact-submit-boxarea .input-area1 button {
  border: none;
  outline: none;
  margin-top: 12px;
}

.contact3-section-area {
  position: relative;
  z-index: 1;
}
.contact3-section-area::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  background: var(--ztc-text-text-7);
  opacity: 0.8;
  z-index: -1;
}
.contact3-section-area .contact-header-area {
  padding: 0 70px 0 0;
}
@media (max-width: 767px) {
  .contact3-section-area .contact-header-area {
    padding: 0;
    margin-bottom: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .contact3-section-area .contact-header-area {
    padding: 0;
    margin-bottom: 30px;
  }
}
.contact3-section-area .contact-header-area h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  border-radius: 4px;
  padding: 8px 12px;
  text-transform: capitalize;
  display: inline-block;
  position: relative;
  z-index: 1;
  margin-bottom: 20px;
}
.contact3-section-area .contact-header-area h5::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  opacity: 10%;
  z-index: -1;
}
.contact3-section-area .contact-header-area h2 {
  color: var(--ztc-text-text-1);
}
.contact3-section-area .contact-header-area p {
  color: var(--ztc-text-text-1);
  opacity: 0.8;
}
.contact3-section-area .contact-header-area .contact-auhtor-side .icons-text {
  display: flex;
  align-items: center;
}
.contact3-section-area .contact-header-area .contact-auhtor-side .icons-text .icons {
  height: 48px;
  width: 48px;
  text-align: center;
  line-height: 48px;
  border-radius: 4px;
  display: inline-block;
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  background: var(--ztc-bg-bg-7);
}
.contact3-section-area .contact-header-area .contact-auhtor-side .icons-text .text {
  padding-left: 16px;
}
.contact3-section-area .contact-header-area .contact-auhtor-side .icons-text .text p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  line-height: 20px;
  color: var(--ztc-text-text-1);
}
.contact3-section-area .contact-header-area .contact-auhtor-side .icons-text .text a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-1);
  display: inline-block;
  transition: all 0.4s;
  margin-top: 12px;
}
.contact3-section-area .contact-header-area .contact-auhtor-side .icons-text .text a:hover {
  color: var(--ztc-bg-bg-7);
  transition: all 0.4s;
}
.contact3-section-area .contact-submit-boxarea {
  padding: 32px 40px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}
.contact3-section-area .contact-submit-boxarea h4 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s32);
  line-height: var(--ztc-font-size-font-s32);
  color: var(--ztc-text-text-1);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 12px;
  text-transform: capitalize;
}
.contact3-section-area .contact-submit-boxarea .input-area {
  margin-top: 20px;
}
.contact3-section-area .contact-submit-boxarea .input-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  margin-bottom: 12px;
}
.contact3-section-area .contact-submit-boxarea .input-area input {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-3);
  width: 100%;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  padding: 16px;
  height: 48px;
}
.contact3-section-area .contact-submit-boxarea .input-area input::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
  opacity: 50%;
}
.contact3-section-area .contact-submit-boxarea .input-area input::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
  opacity: 50%;
}
.contact3-section-area .contact-submit-boxarea .input-area textarea {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-3);
  width: 100%;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  padding: 16px;
  height: 150px;
}
.contact3-section-area .contact-submit-boxarea .input-area textarea::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
  opacity: 50%;
}
.contact3-section-area .contact-submit-boxarea .input-area textarea::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-5);
  opacity: 50%;
}
.contact3-section-area .contact-submit-boxarea .input-area1 {
  margin-top: 20px;
  display: flex;
}
.contact3-section-area .contact-submit-boxarea .input-area1 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  padding-left: 8px;
}
.contact3-section-area .contact-submit-boxarea .input-area1 input[type=checkbox] {
  height: 16px;
  width: 16px;
  border: 1px solid #9EA4A9;
  border-radius: 5px;
  background: none;
  accent-color: var(--ztc-bg-bg-7);
}
.contact3-section-area .contact-submit-boxarea .input-area1 button {
  border: none;
  outline: none;
  margin-top: 12px;
}

.contact4-section-area {
  position: relative;
  z-index: 1;
}
.contact4-section-area::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  background: var(--ztc-text-text-10);
  opacity: 0.4;
  z-index: -1;
}
.contact4-section-area .contact-submit-boxarea {
  padding: 32px 40px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}
.contact4-section-area .contact-submit-boxarea h4 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s32);
  line-height: var(--ztc-font-size-font-s32);
  color: var(--ztc-text-text-1);
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 12px;
  text-transform: capitalize;
}
.contact4-section-area .contact-submit-boxarea .input-area {
  margin-top: 20px;
}
.contact4-section-area .contact-submit-boxarea .input-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  margin-bottom: 12px;
}
.contact4-section-area .contact-submit-boxarea .input-area input {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-3);
  width: 100%;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  padding: 16px;
  height: 48px;
}
.contact4-section-area .contact-submit-boxarea .input-area input::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-10);
  opacity: 50%;
}
.contact4-section-area .contact-submit-boxarea .input-area input::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-10);
  opacity: 50%;
}
.contact4-section-area .contact-submit-boxarea .input-area textarea {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-3);
  width: 100%;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  padding: 16px;
  height: 150px;
}
.contact4-section-area .contact-submit-boxarea .input-area textarea::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-10);
  opacity: 50%;
}
.contact4-section-area .contact-submit-boxarea .input-area textarea::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-10);
  opacity: 50%;
}
.contact4-section-area .contact-submit-boxarea .input-area1 {
  margin-top: 20px;
  display: flex;
}
.contact4-section-area .contact-submit-boxarea .input-area1 p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  font-weight: var(--ztc-weight-medium);
  text-transform: capitalize;
  padding-left: 8px;
}
.contact4-section-area .contact-submit-boxarea .input-area1 input[type=checkbox] {
  height: 16px;
  width: 16px;
  border: 1px solid var(--ztc-bg-bg-11);
  border-radius: 5px;
  background: none;
  accent-color: var(--ztc-bg-bg-11);
}
.contact4-section-area .contact-submit-boxarea .input-area1 button {
  border: none;
  outline: none;
  margin-top: 12px;
}

.contact1-section-area.contact-inner {
  background: var(--ztc-text-text-1);
}
.contact1-section-area.contact-inner::after {
  display: none;
}
.contact1-section-area.contact-inner .contact-header-area h2 {
  color: var(--ztc-text-text-3);
}
.contact1-section-area.contact-inner .contact-header-area h5 {
  background: var(--ztc-bg-bg-2);
  color: var(--ztc-text-text-2);
}
.contact1-section-area.contact-inner .contact-header-area p {
  color: var(--ztc-text-text-4);
}
.contact1-section-area.contact-inner .contact-header-area .contact-auhtor-side .icons-text .text p {
  color: var(--ztc-text-text-3);
}
.contact1-section-area.contact-inner .contact-header-area .contact-auhtor-side .icons-text .text a {
  color: var(--ztc-text-text-3);
}
.contact1-section-area.contact-inner .contact-submit-boxarea {
  background: var(--ztc-bg-bg-1);
}
.contact1-section-area.contact-inner .contact-submit-boxarea h4 {
  color: var(--ztc-text-text-3);
}
.contact1-section-area.contact-inner .contact-submit-boxarea .input-area p {
  color: var(--ztc-text-text-3);
  opacity: 80%;
}
.contact1-section-area.contact-inner .contact-submit-boxarea .input-area input::-moz-placeholder {
  color: var(--ztc-text-text-4);
  opacity: 1;
}
.contact1-section-area.contact-inner .contact-submit-boxarea .input-area input::placeholder {
  color: var(--ztc-text-text-4);
  opacity: 1;
}
.contact1-section-area.contact-inner .contact-submit-boxarea .input-area textarea::-moz-placeholder {
  color: var(--ztc-text-text-4);
  opacity: 1;
}
.contact1-section-area.contact-inner .contact-submit-boxarea .input-area textarea::placeholder {
  color: var(--ztc-text-text-4);
  opacity: 1;
}
.contact1-section-area.contact-inner .contact-submit-boxarea .input-area1 p {
  color: var(--ztc-text-text-3);
  opacity: 80%;
}

.mapouter .gmap_canvas iframe {
  width: 100%;
  height: 565px;
}

/*============= CONTACT CSS AREA ENDS===============*/
/*============= CTA CSS AREA ===============*/
.cta1-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-text-text-2);
  overflow: hidden;
}
.cta1-section-area .cta-header h2 {
  color: var(--ztc-text-text-1);
}
.cta1-section-area .cta-header p {
  color: var(--ztc-text-text-1);
  opacity: 80%;
}
.cta1-section-area .cta-header form {
  background: var(--ztc-text-text-1);
  border-radius: 4px;
  padding: 20px;
  position: relative;
  height: 56px;
  margin-top: 32px;
  display: inline-block;
  width: 385px;
}
@media (max-width: 767px) {
  .cta1-section-area .cta-header form {
    width: 100%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cta1-section-area .cta-header form {
    width: 100%;
  }
}
.cta1-section-area .cta-header form input {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-3);
  font-weight: var(--ztc-weight-medium);
  width: 100%;
  outline: none;
}
.cta1-section-area .cta-header form input::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
}
.cta1-section-area .cta-header form input::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
}
.cta1-section-area .cta-header form button {
  border: none;
  transition: all 0.4s;
  position: absolute;
  right: 4px;
  top: 4px;
}
.cta1-section-area .cta-images .img1 {
  position: absolute;
  text-align: right;
  bottom: 0;
  z-index: 1;
}
@media (max-width: 767px) {
  .cta1-section-area .cta-images .img1 {
    position: relative;
    top: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cta1-section-area .cta-images .img1 {
    position: relative;
    top: 50px;
  }
}
.cta1-section-area .cta-images .img1::after {
  position: absolute;
  content: "";
  height: 470px;
  width: 470px;
  left: 10%;
  transition: all 0.4s;
  z-index: -1;
  background: #CC2431;
  border-radius: 50%;
  bottom: -215px;
  animation-name: animation-5;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}
@media (max-width: 767px) {
  .cta1-section-area .cta-images .img1::after {
    display: none;
  }
}
.cta1-section-area .cta-images .img1 img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.cta4-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-bg-bg-11);
  overflow: hidden;
}
.cta4-section-area .cta-header h2 {
  color: var(--ztc-text-text-1);
}
.cta4-section-area .cta-header p {
  color: var(--ztc-text-text-1);
  opacity: 80%;
}
.cta4-section-area .cta-header form {
  background: var(--ztc-text-text-1);
  border-radius: 4px;
  padding: 20px;
  position: relative;
  height: 56px;
  margin-top: 32px;
  display: inline-block;
  width: 385px;
}
@media (max-width: 767px) {
  .cta4-section-area .cta-header form {
    width: 100%;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cta4-section-area .cta-header form {
    width: 100%;
  }
}
.cta4-section-area .cta-header form input {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-3);
  font-weight: var(--ztc-weight-medium);
  width: 100%;
  outline: none;
}
.cta4-section-area .cta-header form input::-moz-placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
}
.cta4-section-area .cta-header form input::placeholder {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
}
.cta4-section-area .cta-header form button {
  border: none;
  transition: all 0.4s;
  position: absolute;
  right: 4px;
  top: 4px;
}
.cta4-section-area .cta-images .img1 {
  position: absolute;
  text-align: right;
  bottom: 0;
  z-index: 1;
}
@media (max-width: 767px) {
  .cta4-section-area .cta-images .img1 {
    position: relative;
    top: 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cta4-section-area .cta-images .img1 {
    position: relative;
    top: 50px;
  }
}
.cta4-section-area .cta-images .img1::after {
  position: absolute;
  content: "";
  height: 470px;
  width: 470px;
  left: 10%;
  transition: all 0.4s;
  z-index: -1;
  background: #1F59B2;
  border-radius: 50%;
  bottom: -215px;
  animation-name: animation-5;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}
@media (max-width: 767px) {
  .cta4-section-area .cta-images .img1::after {
    display: none;
  }
}
.cta4-section-area .cta-images .img1 img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

/*============= CTA CSS AREA ENDS===============*/
/*============= TESTIMONIAL CSS AREA ===============*/
.testimonial1-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-text-text-3);
}
.testimonial1-section-area .testimonial-header h5 {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  border-radius: 4px;
  padding: 8px 12px;
  text-transform: capitalize;
  display: inline-block;
  position: relative;
  z-index: 1;
  margin-bottom: 20px;
}
.testimonial1-section-area .testimonial-header h5::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  opacity: 10%;
  z-index: -1;
}
.testimonial1-section-area .testimonial-header h2 {
  color: var(--ztc-text-text-1);
}
.testimonial1-section-area .testimonial-header p {
  color: var(--ztc-text-text-1);
  opacity: 80%;
}
.testimonial1-section-area .testimonial-header .btn-area {
  margin-top: 22px;
}
.testimonial1-section-area .testimonial-vertical-slider {
  padding: 0 0 0 50px;
}
@media (max-width: 767px) {
  .testimonial1-section-area .testimonial-vertical-slider {
    padding: 0;
    margin-top: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .testimonial1-section-area .testimonial-vertical-slider {
    padding: 0;
    margin-top: 30px;
  }
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .testimonial3-slider-content-area .testimonial3-author-area {
  position: relative;
  z-index: 1;
  padding: 32px;
  border-radius: 4px;
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .testimonial3-slider-content-area .testimonial3-author-area::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  background: var(--ztc-text-text-1);
  opacity: 5%;
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .testimonial3-slider-content-area .testimonial3-author-area .quito1 {
  position: absolute;
  bottom: 10px;
  right: 10px;
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .testimonial3-slider-content-area .testimonial3-author-area ul {
  margin-bottom: 24px;
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .testimonial3-slider-content-area .testimonial3-author-area ul li {
  display: inline-block;
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .testimonial3-slider-content-area .testimonial3-author-area ul li a {
  height: 32px;
  width: 32px;
  text-align: center;
  line-height: 32px;
  border-radius: 4px;
  transition: all 0.4s;
  display: inline-block;
  color: var(--ztc-text-text-9);
  background: #201F1F;
  margin: 0 4px 0 0;
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .testimonial3-slider-content-area .testimonial3-author-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s40);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  font-style: italic;
}
@media (max-width: 767px) {
  .testimonial1-section-area .testimonial-vertical-slider .slider-galeria .testimonial3-slider-content-area .testimonial3-author-area p {
    font-size: var(--ztc-font-size-font-s20);
    line-height: var(--ztc-font-size-font-s34);
  }
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .testimonial3-slider-content-area .testimonial1-man-info-area {
  display: flex;
  align-items: center;
  margin-top: 32px;
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .testimonial3-slider-content-area .testimonial1-man-info-area .mans-img img {
  height: 90px;
  width: 90px;
  border-radius: 50%;
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .testimonial3-slider-content-area .testimonial1-man-info-area .man-text {
  padding-left: 16px;
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .testimonial3-slider-content-area .testimonial1-man-info-area .man-text a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-1);
  display: inline-block;
  transition: all 0.4s;
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-bold);
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .testimonial3-slider-content-area .testimonial1-man-info-area .man-text p {
  font-size: var(--ztc-font-size-font-s18);
  font-family: var(--ztc-family-font1);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-1);
  opacity: 80%;
  line-height: var(--ztc-font-size-font-s18);
  margin-top: 12px;
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .slider-galeria-thumbs .testimonial1-sliders-img {
  margin-bottom: 15px;
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .slider-galeria-thumbs .testimonial1-sliders-img img {
  height: 60px;
  width: 60px;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}
.testimonial1-section-area .testimonial-vertical-slider .slider-galeria .slider-galeria-thumbs .slick-list.draggable {
  height: 330px;
}
.testimonial1-section-area .testimonial-vertical-slider .testimonial1-sliders-img.slick-slide img {
  margin-bottom: 15px;
  position: relative;
  z-index: 1;
  left: 10px;
  top: 5px;
}
.testimonial1-section-area .testimonial-vertical-slider .testimonial1-sliders-img.slick-slide.slick-current.slick-active {
  position: relative;
}
.testimonial1-section-area .testimonial-vertical-slider .testimonial1-sliders-img.slick-slide.slick-current.slick-active::after {
  position: absolute;
  content: "";
  height: 70px;
  width: 70px;
  border-radius: 50%;
  left: 5px;
  top: 0px;
  transition: all 0.4s;
  background: var(--ztc-text-text-2);
  z-index: -1;
}

.testimonial2-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-bg-bg-1);
}
@media (max-width: 767px) {
  .testimonial2-section-area .testimonial-header {
    margin-bottom: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .testimonial2-section-area .testimonial-header {
    margin-bottom: 30px;
  }
}
.testimonial2-section-area .testimonial-header .btn-area .header-btn3 {
  margin-top: 24px;
}
.testimonial2-section-area .testimonial-sliders {
  position: relative;
}
@media (max-width: 767px) {
  .testimonial2-section-area .testimonial-sliders .testimonial-content-slider .testimonial-slider-boxarea {
    margin-top: 30px;
  }
}
.testimonial2-section-area .testimonial-sliders .testimonial-content-slider .testimonial-slider-boxarea ul {
  margin-bottom: 24px;
}
.testimonial2-section-area .testimonial-sliders .testimonial-content-slider .testimonial-slider-boxarea ul li {
  display: inline-block;
  height: 32px;
  width: 32px;
  text-align: center;
  line-height: 32px;
  border-radius: 4px;
  background: var(--ztc-text-text-1);
  color: #EC811C;
  font-size: var(--ztc-font-size-font-s16);
}
.testimonial2-section-area .testimonial-sliders .testimonial-content-slider .testimonial-slider-boxarea .testimonial5-all-content p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s32);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-6);
}
.testimonial2-section-area .testimonial-sliders .testimonial-content-slider .testimonial-slider-boxarea .content {
  margin-top: 40px;
}
.testimonial2-section-area .testimonial-sliders .testimonial-content-slider .testimonial-slider-boxarea .content a {
  font-size: var(--ztc-font-size-font-s24);
  font-family: var(--ztc-family-font1);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-3);
  display: inline-block;
  font-weight: var(--ztc-weight-bold);
  margin-bottom: 10px;
}
.testimonial2-section-area .testimonial-sliders .testimonial-content-slider .testimonial-slider-boxarea .content p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-6);
  font-weight: var(--ztc-weight-medium);
}
.testimonial2-section-area .testimonial-sliders .testimonial-arrows {
  display: flex;
  align-items: center;
  position: absolute;
  right: 0;
  bottom: 20px;
}
.testimonial2-section-area .testimonial-sliders .testimonial-arrows button {
  height: 48px;
  width: 48px;
  text-align: center;
  line-height: 48px;
  border-radius: 4px;
  transition: all 0.4s;
  color: var(--ztc-text-text-3);
  background: var(--ztc-text-text-1);
  outline: none;
  border: none;
  margin: 0 16px 0 0;
}
.testimonial2-section-area .testimonial-sliders .testimonial-arrows button:hover {
  background: var(--ztc-bg-bg-3);
  transition: all 0.4s;
}

.testimonial3-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-bg-bg-1);
}
.testimonial3-section-area .testimonial3-header {
  text-align: center;
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .testimonial3-section-area .testimonial3-header {
    margin-bottom: 30px;
  }
}
.testimonial3-section-area .testimonial-slider-area .testimonial-boxes {
  text-align: center;
  background: var(--ztc-text-text-1);
  position: relative;
  z-index: 1;
  transition: all 0.4s;
  padding: 32px 45px 32px 45px;
  border-radius: 4px;
}
.testimonial3-section-area .testimonial-slider-area .testimonial-boxes .img1 img {
  height: 80px;
  width: 80px;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
  margin: 0 auto;
}
.testimonial3-section-area .testimonial-slider-area .testimonial-boxes ul {
  margin-top: 32px;
  margin-bottom: 16px;
}
.testimonial3-section-area .testimonial-slider-area .testimonial-boxes ul li {
  display: inline-block;
  color: var(--ztc-text-text-9);
  height: 32px;
  width: 32px;
  text-align: center;
  line-height: 32px;
  transition: all 0.4s;
  background: #FEF3EB;
  border-radius: 4px;
}
.testimonial3-section-area .testimonial-slider-area .testimonial-boxes p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s26);
  color: var(--ztc-text-text-8);
  font-weight: var(--ztc-weight-medium);
  transition: all 0.4s;
}
.testimonial3-section-area .testimonial-slider-area .testimonial-boxes a {
  display: inline-block;
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-7);
  transition: all 0.4s;
  font-family: var(--ztc-family-font1);
  line-height: var(--ztc-font-size-font-s20);
  font-size: var(--ztc-font-size-font-s20);
  margin-top: 28px;
  margin-bottom: 10px;
}
.testimonial3-section-area .testimonial-slider-area .owl-dots {
  text-align: center;
  margin-top: 30px;
}
.testimonial3-section-area .testimonial-slider-area .owl-dots button {
  height: 12px;
  width: 12px;
  display: inline-block;
  border-radius: 50%;
  background: var(--ztc-bg-bg-8);
  margin: 0 6px;
  transition: all 0.4s;
  position: relative;
  z-index: 1;
}
.testimonial3-section-area .testimonial-slider-area .owl-dots button::after {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  display: inline-block;
  border: 1px solid var(--ztc-bg-bg-7);
  top: -4px;
  left: -4px;
  transition: all 0.4s;
  border-radius: 50%;
  visibility: hidden;
  opacity: 0;
}
.testimonial3-section-area .testimonial-slider-area .owl-dots button.active {
  background: var(--ztc-bg-bg-7);
  transition: all 0.4s;
}
.testimonial3-section-area .testimonial-slider-area .owl-dots button.active::after {
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
}

.testimonial4-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-bg-bg-1);
}
.testimonial4-section-area .testimonial-header .btn-area {
  margin-top: 32px;
}
@media (max-width: 767px) {
  .testimonial4-section-area .testimonial-header {
    margin-bottom: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .testimonial4-section-area .testimonial-header {
    margin-bottom: 30px;
  }
}
.testimonial4-section-area .testimonial-slider-boxarea4 .owl-nav {
  position: relative;
  text-align: center;
  margin-top: 30px;
}
@media (max-width: 767px) {
  .testimonial4-section-area .testimonial-slider-boxarea4 .owl-nav {
    position: relative;
    left: 0;
    bottom: 0;
    text-align: start;
    margin-top: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .testimonial4-section-area .testimonial-slider-boxarea4 .owl-nav {
    position: relative;
    left: 0;
    bottom: 0;
    text-align: center;
    margin-top: 30px;
  }
}
.testimonial4-section-area .testimonial-slider-boxarea4 .owl-nav button {
  height: 50px;
  width: 50px;
  text-align: center;
  line-height: 50px;
  display: inline-block;
  border-radius: 4px;
  transition: all 0.4s;
  color: var(--ztc-bg-bg-11);
  background: #C3D2E7;
  font-size: var(--ztc-font-size-font-s16);
  margin: 0 8px 0 0;
}
.testimonial4-section-area .testimonial-slider-boxarea4 .owl-nav button:hover {
  background: var(--ztc-bg-bg-11);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.testimonial4-section-area .testimonial-slider-boxarea4 .tesimonial-slider {
  background: var(--ztc-text-text-1);
  position: relative;
  border-radius: 4px;
  padding: 32px;
}
.testimonial4-section-area .testimonial-slider-boxarea4 .tesimonial-slider ul {
  margin-bottom: 24px;
}
.testimonial4-section-area .testimonial-slider-boxarea4 .tesimonial-slider ul li {
  display: inline-block;
  height: 34px;
  width: 34px;
  text-align: center;
  line-height: 34px;
  border-radius: 4px;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-1);
  color: #EC811C;
}
.testimonial4-section-area .testimonial-slider-boxarea4 .tesimonial-slider p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s26);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-11);
}
.testimonial4-section-area .testimonial-slider-boxarea4 .tesimonial-slider .auhtor-images {
  display: flex;
  align-items: center;
  margin-top: 32px;
}
.testimonial4-section-area .testimonial-slider-boxarea4 .tesimonial-slider .auhtor-images .img1 img {
  height: 80px;
  width: 80px;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}
.testimonial4-section-area .testimonial-slider-boxarea4 .tesimonial-slider .auhtor-images .text {
  margin-left: 16px;
}
.testimonial4-section-area .testimonial-slider-boxarea4 .tesimonial-slider .auhtor-images .text a {
  display: inline-block;
  font-size: var(--ztc-font-size-font-s20);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-10);
  line-height: var(--ztc-font-size-font-s20);
  transition: all 0.4s;
  font-family: var(--ztc-family-font1);
  margin-bottom: 10px;
}

.testimonials-inner-section-area {
  position: relative;
  z-index: 1;
}
.testimonials-inner-section-area .testimonial-inner-boxarea {
  text-align: center;
  position: relative;
  border-radius: 4px;
  padding: 32px 44px;
  background: var(--ztc-bg-bg-1);
  transition: all 0.4s;
  margin-bottom: 30px;
  border: 1px solid var(--ztc-bg-bg-1);
}
.testimonials-inner-section-area .testimonial-inner-boxarea:hover {
  border: 1px solid var(--ztc-text-text-2);
  transition: all 0.4s;
  transform: translateY(-5px);
}
@media (max-width: 767px) {
  .testimonials-inner-section-area .testimonial-inner-boxarea {
    padding: 32px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .testimonials-inner-section-area .testimonial-inner-boxarea {
    padding: 32px;
  }
}
.testimonials-inner-section-area .testimonial-inner-boxarea .img1 img {
  height: 80px;
  width: 80px;
  border-radius: 50%;
}
.testimonials-inner-section-area .testimonial-inner-boxarea .content-area ul {
  margin-top: 32px;
  margin-bottom: 16px;
}
.testimonials-inner-section-area .testimonial-inner-boxarea .content-area ul li {
  display: inline-block;
  height: 32px;
  width: 32px;
  border-radius: 4px;
  transition: all 0.4s;
  text-align: center;
  background: #F3E9E3;
  line-height: 32px;
  color: var(--ztc-bg-bg-7);
}
.testimonials-inner-section-area .testimonial-inner-boxarea .content-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s18);
  line-height: var(--ztc-font-size-font-s26);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-4);
}
.testimonials-inner-section-area .testimonial-inner-boxarea .content-area .text {
  margin-top: 30px;
}
.testimonials-inner-section-area .testimonial-inner-boxarea .content-area .text a {
  display: inline-block;
  font-size: var(--ztc-font-size-font-s20);
  line-height: var(--ztc-font-size-font-s20);
  color: var(--ztc-text-text-3);
  font-weight: var(--ztc-weight-bold);
  transition: all 0.4s;
  font-family: var(--ztc-family-font1);
}
.testimonials-inner-section-area .pagination-area ul {
  text-align: center;
  justify-content: center;
  margin-top: 30px;
}
.testimonials-inner-section-area .pagination-area ul li a {
  height: 50px;
  width: 50px;
  display: inline-block;
  border-radius: 4px !important;
  transition: all 0.4s;
  border: none;
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s40);
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-3);
  margin: 0 16px;
  box-shadow: none;
  background: var(--ztc-bg-bg-1);
}
.testimonials-inner-section-area .pagination-area ul li a:hover {
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
}
.testimonials-inner-section-area .pagination-area ul li .page-link.active {
  background: var(--ztc-text-text-2) !important;
  color: var(--ztc-text-text-1);
}

/*============= TESTIMONIAL CSS AREA ENDS ===============*/
/*============= CASE STUDY CSS AREA ENDS ===============*/
.casestudy-section-area .casestudy-header {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .casestudy-section-area .casestudy-header {
    margin-bottom: 30px;
  }
}
.casestudy-section-area .casestudy-slider-area .case-author-boxarea {
  position: relative;
  z-index: 1;
  transition: all 0.4s;
}
.casestudy-section-area .casestudy-slider-area .case-author-boxarea:hover .imges::after {
  height: 100%;
  width: 100%;
  transition: all 0.4s;
}
.casestudy-section-area .casestudy-slider-area .case-author-boxarea:hover .imges img {
  transform: scale(1.1) rotate(4deg);
  transition: all 0.4s;
}
.casestudy-section-area .casestudy-slider-area .case-author-boxarea:hover .case-content .icons a {
  background: var(--ztc-bg-bg-3);
  transition: all 0.4s;
}
.casestudy-section-area .casestudy-slider-area .case-author-boxarea .imges {
  overflow: hidden;
  position: relative;
  transition: all 0.4s;
  border-radius: 4px;
}
.casestudy-section-area .casestudy-slider-area .case-author-boxarea .imges::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-3);
  opacity: 0.7;
}
.casestudy-section-area .casestudy-slider-area .case-author-boxarea .imges img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 4px;
  transition: all 0.4s;
}
.casestudy-section-area .casestudy-slider-area .case-author-boxarea .case-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--ztc-text-text-1);
  padding: 20px 24px;
  border-radius: 4px;
  position: relative;
  bottom: 0;
  margin: -120px 16px 16px 16px;
}
.casestudy-section-area .casestudy-slider-area .case-author-boxarea .case-content .text p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-6);
  font-weight: var(--ztc-weight-medium);
  margin-bottom: 14px;
}
.casestudy-section-area .casestudy-slider-area .case-author-boxarea .case-content .text a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-5);
  display: inline-block;
  font-weight: var(--ztc-weight-bold);
}
@media (max-width: 767px) {
  .casestudy-section-area .casestudy-slider-area .case-author-boxarea .case-content .text a {
    font-size: var(--ztc-font-size-font-s20);
    line-height: 20px;
  }
}
.casestudy-section-area .casestudy-slider-area .case-author-boxarea .case-content .icons a {
  height: 48px;
  width: 48px;
  text-align: center;
  line-height: 48px;
  transition: all 0.4s;
  border-radius: 50%;
  background: var(--ztc-text-text-5);
  color: var(--ztc-text-text-1);
  transform: rotate(-45deg);
  display: inline-block;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .casestudy-section-area .casestudy-slider-area .owl-nav {
    text-align: center;
    margin-top: 30px;
  }
}
@media (max-width: 767px) {
  .casestudy-section-area .casestudy-slider-area .owl-nav {
    text-align: center;
    margin-top: 30px;
  }
}
.casestudy-section-area .casestudy-slider-area .owl-nav button {
  height: 60px;
  width: 60px;
  display: inline-block;
  border-radius: 4px;
  text-align: center;
  transition: all 0.4s;
  background: #FFEFC5;
  color: var(--ztc-text-text-5);
}
.casestudy-section-area .casestudy-slider-area .owl-nav button:hover {
  background: var(--ztc-bg-bg-3);
  transition: all 0.4s;
}
.casestudy-section-area .casestudy-slider-area .owl-nav .owl-prev {
  position: absolute;
  left: -82px;
  top: 41%;
  bottom: 50%;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .casestudy-section-area .casestudy-slider-area .owl-nav .owl-prev {
    position: relative;
    right: 0;
    left: 0;
    top: 0;
    bottom: 0;
    margin: 0 10px 0 0;
  }
}
@media (max-width: 767px) {
  .casestudy-section-area .casestudy-slider-area .owl-nav .owl-prev {
    position: relative;
    right: 0;
    left: 0;
    top: 0;
    bottom: 0;
    margin: 0 10px 0 0;
  }
}
.casestudy-section-area .casestudy-slider-area .owl-nav .owl-next {
  position: absolute;
  right: -82px;
  top: 41%;
  bottom: 50%;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .casestudy-section-area .casestudy-slider-area .owl-nav .owl-next {
    position: relative;
    right: 0;
    top: 0;
    bottom: 0;
  }
}
@media (max-width: 767px) {
  .casestudy-section-area .casestudy-slider-area .owl-nav .owl-next {
    position: relative;
    right: 0;
    top: 0;
    bottom: 0;
  }
}

.casestudy2-section-area .casestudy-header {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .casestudy2-section-area .casestudy-header {
    margin-bottom: 30px;
  }
}
.casestudy2-section-area .case-auhtor-boxarea {
  position: relative;
  z-index: 1;
  overflow: hidden;
  border-radius: 4px;
  transition: all 0.4s;
  margin-bottom: 30px;
}
.casestudy2-section-area .case-auhtor-boxarea:hover .img1 img {
  transform: scale(1.1) rotate(-4deg);
  transition: all 0.4s;
}
.casestudy2-section-area .case-auhtor-boxarea:hover .case-bg {
  transition: all 0.6s;
  bottom: 0;
}
.casestudy2-section-area .case-auhtor-boxarea .img1 {
  overflow: hidden;
  border-radius: 4px;
  transition: all 0.4s;
  position: relative;
}
.casestudy2-section-area .case-auhtor-boxarea .img1 img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 4px;
  transition: all 0.4s;
}
.casestudy2-section-area .case-auhtor-boxarea .case-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: -300px;
  z-index: 1;
  transition: all 0.6s;
}
.casestudy2-section-area .case-auhtor-boxarea .case-bg img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 4px;
}
.casestudy2-section-area .case-auhtor-boxarea .content {
  position: absolute;
  bottom: 38px;
  z-index: 2;
  left: 24px;
}
.casestudy2-section-area .case-auhtor-boxarea .content span {
  height: 48px;
  width: 48px;
  text-align: center;
  line-height: 48px;
  text-align: center;
  display: inline-block;
  color: var(--ztc-text-text-1);
  border-radius: 50%;
  transform: rotate(-45deg);
  transition: all 0.4s;
  background: var(--ztc-bg-bg-7);
}
.casestudy2-section-area .case-auhtor-boxarea .content p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  font-weight: var(--ztc-weight-medium);
  margin-top: 24px;
  margin-bottom: 16px;
}
.casestudy2-section-area .case-auhtor-boxarea .content a {
  display: inline-block;
  transition: all 0.4s;
  font-size: var(--ztc-font-size-font-s22);
  line-height: var(--ztc-font-size-font-s22);
  color: var(--ztc-text-text-1);
  font-weight: var(--ztc-weight-bold);
  font-family: var(--ztc-bg-bg-7);
}

.casestudy4-section-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-text-text-10);
}
.casestudy4-section-area .case-header {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .casestudy4-section-area .case-header {
    margin-bottom: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .casestudy4-section-area .case-header {
    margin-bottom: 30px;
  }
}
.casestudy4-section-area .case-boxes-area {
  position: relative;
  z-index: 1;
  border-radius: 4px;
  transition: all 0.4s;
  margin-bottom: 30px;
}
.casestudy4-section-area .case-boxes-area:hover .img1::after {
  height: 100%;
  transition: all 0.4s;
}
.casestudy4-section-area .case-boxes-area:hover .img1 img {
  transform: scale(1.1) rotate(-4deg);
  transition: all 0.4s;
}
.casestudy4-section-area .case-boxes-area:hover .content-area::after {
  height: 102%;
  transition: all 0.4s;
}
.casestudy4-section-area .case-boxes-area:hover .content-area .icons {
  background: var(--ztc-text-text-1);
  transition: all 0.4s;
  transform: rotateY(-180deg);
}
.casestudy4-section-area .case-boxes-area:hover .content-area .icons img {
  filter: none;
  transition: all 0.4s;
}
.casestudy4-section-area .case-boxes-area:hover .content-area a {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.casestudy4-section-area .case-boxes-area:hover .content-area a.readmore {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
}
.casestudy4-section-area .case-boxes-area:hover .content-area p {
  color: var(--ztc-text-text-1);
  transition: all 0.4s;
  opacity: 80%;
}
.casestudy4-section-area .case-boxes-area .img1 {
  overflow: hidden;
  position: relative;
  transition: all 0.4s;
  border-radius: 4px;
}
.casestudy4-section-area .case-boxes-area .img1::after {
  position: absolute;
  content: "";
  height: 0px;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-11);
  opacity: 0.7;
  border-radius: 4px;
}
.casestudy4-section-area .case-boxes-area .img1 img {
  height: 100%;
  width: 100%;
  transition: all 0.4s;
  border-radius: 4px;
}
.casestudy4-section-area .case-boxes-area .content-area {
  position: relative;
  z-index: 1;
  background: var(--ztc-text-text-1);
  transition: all 0.4s;
  text-align: center;
  padding: 72px 32px 32px 32px;
  border-radius: 4px;
  position: relative;
  z-index: 2;
  margin: -130px 24px 0 24px;
}
.casestudy4-section-area .case-boxes-area .content-area::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  left: 0;
  bottom: -1px;
  transition: all 0.4s;
  background: var(--ztc-bg-bg-11);
  border-radius: 4px;
  z-index: -1;
}
.casestudy4-section-area .case-boxes-area .content-area .icons {
  height: 80px;
  width: 80px;
  text-align: center;
  line-height: 80px;
  border-radius: 50%;
  display: inline-block;
  background: var(--ztc-bg-bg-11);
  transition: all 0.4s;
  position: absolute;
  left: 40%;
  top: -32px;
  right: 50%;
}
.casestudy4-section-area .case-boxes-area .content-area .icons img {
  transition: all 0.4s;
  filter: brightness(0) invert(1);
}
.casestudy4-section-area .case-boxes-area .content-area a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-10);
  transition: all 0.4s;
  display: block;
}
@media (max-width: 767px) {
  .casestudy4-section-area .case-boxes-area .content-area a {
    font-size: var(--ztc-font-size-font-s20);
    line-height: 26px;
  }
}
.casestudy4-section-area .case-boxes-area .content-area p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s24);
  font-weight: var(--ztc-weight-medium);
  color: var(--ztc-text-text-11);
  transition: all 0.4s;
  margin-top: 16px;
}
.casestudy4-section-area .case-boxes-area .content-area a.readmore {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  font-weight: var(--ztc-weight-bold);
  color: var(--ztc-text-text-10);
  transition: all 0.4s;
  margin-bottom: 0;
  margin-top: 24px;
  display: inline-block;
}

.casestudy-inner-section-area .casestudy-header {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .casestudy-inner-section-area .casestudy-header {
    margin-bottom: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .casestudy-inner-section-area .casestudy-header {
    margin-bottom: 30px;
  }
}
.casestudy-inner-section-area .case-author-boxarea {
  position: relative;
  z-index: 1;
  transition: all 0.4s;
  margin-bottom: 53px;
}
.casestudy-inner-section-area .case-author-boxarea:hover .imges::after {
  height: 100%;
  width: 100%;
  transition: all 0.4s;
}
.casestudy-inner-section-area .case-author-boxarea:hover .imges img {
  transform: scale(1.1) rotate(4deg);
  transition: all 0.4s;
}
.casestudy-inner-section-area .case-author-boxarea:hover .case-content .icons a {
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
}
.casestudy-inner-section-area .case-author-boxarea .imges {
  overflow: hidden;
  position: relative;
  transition: all 0.4s;
  border-radius: 4px;
}
.casestudy-inner-section-area .case-author-boxarea .imges::after {
  position: absolute;
  content: "";
  height: 0;
  width: 100%;
  left: 0;
  top: 0;
  transition: all 0.4s;
  background: var(--ztc-text-text-2);
  opacity: 0.7;
}
.casestudy-inner-section-area .case-author-boxarea .imges img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 4px;
  transition: all 0.4s;
}
.casestudy-inner-section-area .case-author-boxarea .case-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--ztc-text-text-1);
  padding: 20px 24px;
  border-radius: 4px;
  position: relative;
  bottom: 0;
  margin: -120px 16px 16px 16px;
}
.casestudy-inner-section-area .case-author-boxarea .case-content .text p {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s16);
  color: var(--ztc-text-text-4);
  font-weight: var(--ztc-weight-medium);
  margin-bottom: 14px;
}
.casestudy-inner-section-area .case-author-boxarea .case-content .text a {
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s24);
  line-height: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-3);
  display: inline-block;
  font-weight: var(--ztc-weight-bold);
}
@media (max-width: 767px) {
  .casestudy-inner-section-area .case-author-boxarea .case-content .text a {
    font-size: var(--ztc-font-size-font-s20);
    line-height: 20px;
  }
}
.casestudy-inner-section-area .case-author-boxarea .case-content .icons a {
  height: 48px;
  width: 48px;
  text-align: center;
  line-height: 48px;
  transition: all 0.4s;
  border-radius: 50%;
  background: #FAE7E8;
  color: var(--ztc-text-text-2);
  transform: rotate(-45deg);
  display: inline-block;
}
.casestudy-inner-section-area .pagination-area ul {
  text-align: center;
  justify-content: center;
  margin-top: 30px;
}
.casestudy-inner-section-area .pagination-area ul li a {
  height: 50px;
  width: 50px;
  display: inline-block;
  border-radius: 4px !important;
  transition: all 0.4s;
  border: none;
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s16);
  line-height: var(--ztc-font-size-font-s40);
  font-weight: var(--ztc-weight-semibold);
  color: var(--ztc-text-text-3);
  margin: 0 16px;
  box-shadow: none;
  background: var(--ztc-bg-bg-1);
}
.casestudy-inner-section-area .pagination-area ul li a:hover {
  background: var(--ztc-text-text-2);
  transition: all 0.4s;
  color: var(--ztc-text-text-1);
}
.casestudy-inner-section-area .pagination-area ul li .page-link.active {
  background: var(--ztc-text-text-2) !important;
  color: var(--ztc-text-text-1);
}

/*============= CASE STUDY CSS AREA ENDS ===============*//*# sourceMappingURL=main.css.map */